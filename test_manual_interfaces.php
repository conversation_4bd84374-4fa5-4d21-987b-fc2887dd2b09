<?php
/**
 * 手动退票和改签接口测试脚本
 * 
 * 使用方法:
 * php test_manual_interfaces.php
 */

// 测试配置
$baseUrl = 'http://localhost/cytms'; // 修改为你的项目URL
$testData = [
    'refund' => [
        'origin_order_id' => 1,
        'origin_order_no' => 'T202501020001',
        'origin_pnr' => 'ABC123',
        'area_type' => 1,
        'passengers' => [
            [
                'person_name' => '张三',
                'doc_id' => '110101199001011234',
                'telephone' => '13800138000',
                'passenger_type' => 1,
                'ticket_number' => '999-1234567890',
                'original_price' => 1000.00,
                'refund_amount' => 800.00,
                'refund_fee' => 200.00,
                'service_fee' => 50.00
            ]
        ],
        'segments' => [
            [
                'flight_number' => 'CZ3101',
                'departure_datetime' => '2025-01-15 08:00:00',
                'departure_airport' => 'CAN',
                'arrival_datetime' => '2025-01-15 10:30:00',
                'arrival_airport' => 'PEK',
                'cabin' => 'Y'
            ]
        ],
        'contact_name' => '张三',
        'contact_telephone' => '13800138000',
        'contact_email' => '<EMAIL>',
        'refund_reason' => 1,
        'remark' => '测试退票'
    ],
    'rebook' => [
        'origin_order_id' => 1,
        'origin_order_no' => 'T202501020001',
        'origin_pnr' => 'ABC123',
        'area_type' => 1,
        'passengers' => [
            [
                'person_name' => '张三',
                'doc_id' => '110101199001011234',
                'telephone' => '13800138000',
                'passenger_type' => 1,
                'old_ticket_number' => '999-1234567890',
                'new_ticket_number' => '999-1234567891',
                'original_price' => 1000.00,
                'new_price' => 1200.00,
                'price_difference' => 200.00,
                'change_fee' => 100.00,
                'service_fee' => 50.00
            ]
        ],
        'old_segments' => [
            [
                'flight_number' => 'CZ3101',
                'departure_datetime' => '2025-01-15 08:00:00',
                'departure_airport' => 'CAN',
                'arrival_datetime' => '2025-01-15 10:30:00',
                'arrival_airport' => 'PEK',
                'cabin' => 'Y'
            ]
        ],
        'new_segments' => [
            [
                'flight_number' => 'CZ3103',
                'departure_datetime' => '2025-01-15 14:00:00',
                'departure_airport' => 'CAN',
                'arrival_datetime' => '2025-01-15 16:30:00',
                'arrival_airport' => 'PEK',
                'cabin' => 'Y'
            ]
        ],
        'contact_name' => '张三',
        'contact_telephone' => '13800138000',
        'contact_email' => '<EMAIL>',
        'rebook_reason' => 1,
        'remark' => '测试改签',
        'new_pnr' => 'DEF456'
    ]
];

/**
 * 发送HTTP请求
 */
function sendRequest($url, $data) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

/**
 * 测试手动退票接口
 */
function testRefundInterface($baseUrl, $data) {
    echo "=== 测试手动退票接口 ===\n";
    echo "URL: {$baseUrl}/admin/order/manual/refund\n";
    echo "请求数据: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    $result = sendRequest("{$baseUrl}/admin/order/manual/refund", $data);
    
    echo "HTTP状态码: {$result['http_code']}\n";
    if ($result['error']) {
        echo "错误信息: {$result['error']}\n";
    }
    
    if ($result['response']) {
        $response = json_decode($result['response'], true);
        if ($response) {
            echo "响应结果: " . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
            if (isset($response['code']) && $response['code'] == 1) {
                echo "✅ 退票接口测试成功\n";
                return $response['data']['refund_order_id'] ?? null;
            } else {
                echo "❌ 退票接口测试失败: " . ($response['msg'] ?? '未知错误') . "\n";
            }
        } else {
            echo "❌ 响应解析失败: {$result['response']}\n";
        }
    } else {
        echo "❌ 无响应数据\n";
    }
    
    echo "\n";
    return null;
}

/**
 * 测试手动改签接口
 */
function testRebookInterface($baseUrl, $data) {
    echo "=== 测试手动改签接口 ===\n";
    echo "URL: {$baseUrl}/admin/order/manual/rebook\n";
    echo "请求数据: " . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    
    $result = sendRequest("{$baseUrl}/admin/order/manual/rebook", $data);
    
    echo "HTTP状态码: {$result['http_code']}\n";
    if ($result['error']) {
        echo "错误信息: {$result['error']}\n";
    }
    
    if ($result['response']) {
        $response = json_decode($result['response'], true);
        if ($response) {
            echo "响应结果: " . json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
            if (isset($response['code']) && $response['code'] == 1) {
                echo "✅ 改签接口测试成功\n";
                return $response['data']['rebook_order_id'] ?? null;
            } else {
                echo "❌ 改签接口测试失败: " . ($response['msg'] ?? '未知错误') . "\n";
            }
        } else {
            echo "❌ 响应解析失败: {$result['response']}\n";
        }
    } else {
        echo "❌ 无响应数据\n";
    }
    
    echo "\n";
    return null;
}

/**
 * 测试参数验证
 */
function testValidation($baseUrl) {
    echo "=== 测试参数验证 ===\n";
    
    // 测试空数据
    $emptyData = [];
    echo "测试空数据...\n";
    $result = sendRequest("{$baseUrl}/admin/order/manual/refund", $emptyData);
    $response = json_decode($result['response'], true);
    if (isset($response['code']) && $response['code'] == 0) {
        echo "✅ 空数据验证正常: " . $response['msg'] . "\n";
    } else {
        echo "❌ 空数据验证异常\n";
    }
    
    // 测试无效订单ID
    $invalidData = [
        'origin_order_id' => 99999,
        'origin_order_no' => 'INVALID',
        'origin_pnr' => 'INVALID',
        'area_type' => 1,
        'passengers' => [],
        'segments' => [],
        'contact_name' => 'test',
        'contact_telephone' => '13800138000',
        'refund_reason' => 1
    ];
    echo "测试无效订单ID...\n";
    $result = sendRequest("{$baseUrl}/admin/order/manual/refund", $invalidData);
    $response = json_decode($result['response'], true);
    if (isset($response['code']) && $response['code'] == 0) {
        echo "✅ 无效订单验证正常: " . $response['msg'] . "\n";
    } else {
        echo "❌ 无效订单验证异常\n";
    }
    
    echo "\n";
}

// 主测试流程
echo "手动退票和改签接口测试开始\n";
echo "测试时间: " . date('Y-m-d H:i:s') . "\n";
echo "基础URL: {$baseUrl}\n\n";

// 测试参数验证
testValidation($baseUrl);

// 测试退票接口
$refundOrderId = testRefundInterface($baseUrl, $testData['refund']);

// 测试改签接口  
$rebookOrderId = testRebookInterface($baseUrl, $testData['rebook']);

// 测试总结
echo "=== 测试总结 ===\n";
if ($refundOrderId) {
    echo "✅ 退票接口测试通过，生成退票订单ID: {$refundOrderId}\n";
}
if ($rebookOrderId) {
    echo "✅ 改签接口测试通过，生成改签订单ID: {$rebookOrderId}\n";
}

echo "\n注意事项:\n";
echo "1. 请确保测试数据中的原订单在数据库中存在\n";
echo "2. 请确保航班号和机场代码在基础数据表中存在\n";
echo "3. 如果测试失败，请检查数据库连接和表结构\n";
echo "4. 建议在测试环境中运行此脚本\n";

echo "\n测试完成!\n";
?>
