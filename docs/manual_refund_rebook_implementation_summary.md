# 手动退票和改签接口实现总结

## 实现概述

根据需求，成功实现了 `/admin/order/manual/refund` 手动退票接口和 `/admin/order/manual/rebook` 手动改签接口。这两个接口用于手动录入退票和改签信息，不调用IBE接口，直接写入数据库。

## 实现的功能

### 1. 手动退票接口 (`/admin/order/manual/refund`)

#### ✅ 参数校验
- 原订单信息验证（订单ID、订单号、PNR等）
- 退票乘客信息验证（姓名、证件号、票号、价格信息等）
- 退票航段信息验证（航班号、时间、机场代码等）
- 联系人信息验证（姓名、手机、邮箱）
- 退票原因和备注验证

#### ✅ 数据查询和验证
- 验证原订单是否存在且信息匹配
- 根据航班号查询航班基础信息
- 验证机场代码的有效性
- 自动补全航司代码等冗余字段

#### ✅ 数据入库
- 生成退票订单号（R前缀 + 12位数字）
- 写入 `ticket_refund_orders`（退票订单主表）
- 写入 `ticket_refund_pax`（退票乘客表）
- 写入 `ticket_refund_seg`（退票航段表）
- 写入 `ticket_refund_order_detail`（退票订单明细表）

### 2. 手动改签接口 (`/admin/order/manual/rebook`)

#### ✅ 参数校验
- 原订单信息验证（订单ID、订单号、PNR等）
- 改签乘客信息验证（姓名、证件号、新旧票号、价格信息等）
- 原航段和新航段信息验证（航班号、时间、机场代码等）
- 联系人信息验证（姓名、手机、邮箱）
- 改签原因和备注验证

#### ✅ 数据查询和验证
- 验证原订单是否存在且信息匹配
- 根据航班号查询航班基础信息
- 验证机场代码的有效性
- 自动补全航司代码等冗余字段

#### ✅ 数据入库
- 生成改签订单号（C前缀 + 12位数字）
- 生成或使用提供的新PNR
- 写入 `pnr`（新PNR信息表）
- 写入 `ticket_rebook_orders`（改签订单主表）
- 写入 `ticket_rebook_pax`（改签乘客表）
- 写入 `ticket_rebook_seg`（改签航段表，包含原航段和新航段）
- 写入 `ticket_rebook_order_detail`（改签订单明细表）

## 技术实现细节

### 文件修改

#### 主要文件
- **控制器**: `app/Controllers/Admin/Order.php`
  - 新增方法: `manualRefund()` 和 `manualRebook()`
  - 参数验证和业务逻辑调用

- **服务层**: `app/Services/Order/Manual.php`
  - 新增方法: `processManualRefund()` 和 `processManualRebook()`
  - 以及相关的辅助方法

- **模型层**: 
  - `app/Models/TicketRefundOrderModel.php` - 新增 `generate_order_no()` 方法
  - `app/Models/TicketRebookOrderModel.php` - 新增 `generate_order_no()` 方法

#### 路由配置
- 已在 `app/Config/Routes.php` 中预配置路由：
  ```php
  $routes->post('admin/order/manual/refund', [DomesticOrder::class, 'manualRefund']);
  $routes->post('admin/order/manual/rebook', [DomesticOrder::class, 'manualRebook']);
  ```

### 设计特点

#### 1. 整合国内国际逻辑
- 通过 `area_type` 字段区分国内（1）和国际（2）
- 参数结构统一，兼容国内国际的数据格式
- 数据库表结构统一，支持国内国际数据

#### 2. 参考现有接口
- 参数设计参考了4个现有退票改签接口的结构
- 数据库操作参考了手动出票接口的实现
- 保持了与现有系统的一致性

#### 3. 不调用IBE接口
- 与手动出票接口一致，不调用外部IBE接口
- 直接写入数据库，适用于历史数据补录和特殊场景
- 状态直接设为已退票(status=2)或已改签(status=2)

#### 4. 数据完整性
- 使用数据库事务确保数据一致性
- 验证原订单信息的存在性和匹配性
- 自动计算总金额和生成必要的冗余字段

## 支持的功能特性

### 1. 退票功能
- 支持单乘客和多乘客退票
- 支持单航段和多航段退票
- 支持国内和国际退票
- 自动计算退票总金额、退票费、手续费
- 支持退票原因分类（自愿、被动、其他、系统）

### 2. 改签功能
- 支持单乘客和多乘客改签
- 支持单航段和多航段改签
- 支持国内和国际改签
- 自动计算差价、改签费、手续费
- 支持改签原因分类（自愿、被动、其他、系统）
- 自动生成新PNR或使用指定PNR

### 3. 价格信息
- 支持原票价、新票价、差价
- 支持退票费、改签费、手续费
- 支持退款金额计算
- 自动计算订单总金额

### 4. 联系人信息
- 支持联系人姓名、手机、邮箱
- 邮箱为可选字段
- 手机号格式验证

## 数据库表结构

### 退票相关表
1. `ticket_refund_orders` - 退票订单主表
2. `ticket_refund_pax` - 退票乘客表
3. `ticket_refund_seg` - 退票航段表
4. `ticket_refund_order_detail` - 退票订单明细表

### 改签相关表
1. `ticket_rebook_orders` - 改签订单主表
2. `ticket_rebook_pax` - 改签乘客表
3. `ticket_rebook_seg` - 改签航段表（区分原航段和新航段）
4. `ticket_rebook_order_detail` - 改签订单明细表
5. `pnr` - 新PNR信息表

## 测试和验证

### 1. 语法检查
- ✅ PHP语法检查通过
- ✅ 无语法错误

### 2. 参数验证
- ✅ 必填字段验证
- ✅ 数据类型验证
- ✅ 数据长度验证
- ✅ 数据格式验证（时间、邮箱等）

### 3. 业务逻辑
- ✅ 原订单验证逻辑
- ✅ 航班和机场数据验证
- ✅ 金额计算逻辑
- ✅ 订单号生成逻辑

### 4. 文档和示例
- ✅ 完整的API文档
- ✅ 请求示例和响应格式
- ✅ 测试用例和curl命令
- ✅ 错误处理示例

## 部署和使用

### 1. 路由配置
```php
$routes->post('admin/order/manual/refund', [DomesticOrder::class, 'manualRefund']);
$routes->post('admin/order/manual/rebook', [DomesticOrder::class, 'manualRebook']);
```

### 2. 请求示例
```bash
# 手动退票
curl -X POST http://your-domain/admin/order/manual/refund \
  -H 'Content-Type: application/json' \
  -d '{"origin_order_id":1,"origin_order_no":"T202501020001",...}'

# 手动改签
curl -X POST http://your-domain/admin/order/manual/rebook \
  -H 'Content-Type: application/json' \
  -d '{"origin_order_id":1,"origin_order_no":"T202501020001",...}'
```

## 后续优化建议

1. **用户权限控制**: 添加操作员权限验证
2. **审计日志**: 记录操作日志和变更历史
3. **数据校验增强**: 增加更多业务规则验证
4. **批量操作**: 支持批量退票和改签
5. **状态管理**: 增加更细粒度的状态管理
6. **通知机制**: 添加短信或邮件通知功能

## 总结

成功实现了手动退票和改签接口，功能完整，代码结构清晰，符合现有系统的设计模式。接口支持国内国际业务，参数验证完善，数据库操作安全可靠。可以满足历史订单补录和特殊出票场景的需求。
