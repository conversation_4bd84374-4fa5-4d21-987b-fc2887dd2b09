# 手动退票和改签接口测试

## 测试环境准备

在测试之前，请确保：
1. 数据库中存在测试用的原订单数据
2. 相关的航班和机场数据已存在
3. Web服务器正常运行

## 1. 手动退票接口测试

### 测试用例1：基本退票功能

```bash
curl -X POST http://your-domain/admin/order/manual/refund \
  -H 'Content-Type: application/json' \
  -d '{
    "origin_order_id": 1,
    "origin_order_no": "T202501020001",
    "origin_pnr": "ABC123",
    "area_type": 1,
    "passengers": [
        {
            "person_name": "张三",
            "doc_id": "110101199001011234",
            "telephone": "13800138000",
            "passenger_type": 1,
            "ticket_number": "999-1234567890",
            "original_price": 1000.00,
            "refund_amount": 800.00,
            "refund_fee": 200.00,
            "service_fee": 50.00
        }
    ],
    "segments": [
        {
            "flight_number": "CZ3101",
            "departure_datetime": "2025-01-15 08:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 10:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "contact_name": "张三",
    "contact_telephone": "13800138000",
    "contact_email": "<EMAIL>",
    "refund_reason": 1,
    "remark": "自愿退票测试"
}'
```

### 测试用例2：多乘客退票

```bash
curl -X POST http://your-domain/admin/order/manual/refund \
  -H 'Content-Type: application/json' \
  -d '{
    "origin_order_id": 2,
    "origin_order_no": "T202501020002",
    "origin_pnr": "DEF456",
    "area_type": 1,
    "passengers": [
        {
            "person_name": "张三",
            "doc_id": "110101199001011234",
            "telephone": "13800138000",
            "passenger_type": 1,
            "ticket_number": "999-1234567890",
            "original_price": 1000.00,
            "refund_amount": 800.00,
            "refund_fee": 200.00,
            "service_fee": 50.00
        },
        {
            "person_name": "李四",
            "doc_id": "110101199002022345",
            "telephone": "13800138001",
            "passenger_type": 1,
            "ticket_number": "999-1234567891",
            "original_price": 1000.00,
            "refund_amount": 800.00,
            "refund_fee": 200.00,
            "service_fee": 50.00
        }
    ],
    "segments": [
        {
            "flight_number": "CZ3101",
            "departure_datetime": "2025-01-15 08:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 10:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "contact_name": "张三",
    "contact_telephone": "13800138000",
    "refund_reason": 1,
    "remark": "多乘客退票测试"
}'
```

## 2. 手动改签接口测试

### 测试用例1：基本改签功能

```bash
curl -X POST http://your-domain/admin/order/manual/rebook \
  -H 'Content-Type: application/json' \
  -d '{
    "origin_order_id": 1,
    "origin_order_no": "T202501020001",
    "origin_pnr": "ABC123",
    "area_type": 1,
    "passengers": [
        {
            "person_name": "张三",
            "doc_id": "110101199001011234",
            "telephone": "13800138000",
            "passenger_type": 1,
            "old_ticket_number": "999-1234567890",
            "new_ticket_number": "999-1234567891",
            "original_price": 1000.00,
            "new_price": 1200.00,
            "price_difference": 200.00,
            "change_fee": 100.00,
            "service_fee": 50.00
        }
    ],
    "old_segments": [
        {
            "flight_number": "CZ3101",
            "departure_datetime": "2025-01-15 08:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 10:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "new_segments": [
        {
            "flight_number": "CZ3103",
            "departure_datetime": "2025-01-15 14:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 16:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "contact_name": "张三",
    "contact_telephone": "13800138000",
    "contact_email": "<EMAIL>",
    "rebook_reason": 1,
    "remark": "自愿改签测试",
    "new_pnr": "GHI789"
}'
```

### 测试用例2：往返程改签

```bash
curl -X POST http://your-domain/admin/order/manual/rebook \
  -H 'Content-Type: application/json' \
  -d '{
    "origin_order_id": 3,
    "origin_order_no": "T202501020003",
    "origin_pnr": "JKL012",
    "area_type": 1,
    "passengers": [
        {
            "person_name": "王五",
            "doc_id": "110101199003033456",
            "telephone": "13800138002",
            "passenger_type": 1,
            "old_ticket_number": "999-1234567892",
            "new_ticket_number": "999-1234567893",
            "original_price": 2000.00,
            "new_price": 2400.00,
            "price_difference": 400.00,
            "change_fee": 200.00,
            "service_fee": 100.00
        }
    ],
    "old_segments": [
        {
            "flight_number": "CZ3101",
            "departure_datetime": "2025-01-15 08:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 10:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        },
        {
            "flight_number": "CZ3102",
            "departure_datetime": "2025-01-20 15:00:00",
            "departure_airport": "PEK",
            "arrival_datetime": "2025-01-20 17:30:00",
            "arrival_airport": "CAN",
            "cabin": "Y"
        }
    ],
    "new_segments": [
        {
            "flight_number": "CZ3103",
            "departure_datetime": "2025-01-15 14:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 16:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        },
        {
            "flight_number": "CZ3104",
            "departure_datetime": "2025-01-20 18:00:00",
            "departure_airport": "PEK",
            "arrival_datetime": "2025-01-20 20:30:00",
            "arrival_airport": "CAN",
            "cabin": "Y"
        }
    ],
    "contact_name": "王五",
    "contact_telephone": "13800138002",
    "rebook_reason": 1,
    "remark": "往返程改签测试"
}'
```

## 3. 错误测试用例

### 测试用例1：原订单不存在

```bash
curl -X POST http://your-domain/admin/order/manual/refund \
  -H 'Content-Type: application/json' \
  -d '{
    "origin_order_id": 99999,
    "origin_order_no": "T999999999999",
    "origin_pnr": "XXX999",
    "area_type": 1,
    "passengers": [
        {
            "person_name": "测试",
            "doc_id": "110101199001011234",
            "telephone": "13800138000",
            "passenger_type": 1,
            "ticket_number": "999-1234567890",
            "original_price": 1000.00,
            "refund_amount": 800.00,
            "refund_fee": 200.00
        }
    ],
    "segments": [
        {
            "flight_number": "CZ3101",
            "departure_datetime": "2025-01-15 08:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 10:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "contact_name": "测试",
    "contact_telephone": "13800138000",
    "refund_reason": 1
}'
```

### 测试用例2：参数验证错误

```bash
curl -X POST http://your-domain/admin/order/manual/refund \
  -H 'Content-Type: application/json' \
  -d '{
    "origin_order_id": "invalid",
    "origin_order_no": "",
    "origin_pnr": "TOOLONG",
    "area_type": 5,
    "passengers": [],
    "segments": [],
    "contact_name": "",
    "contact_telephone": "invalid_phone",
    "refund_reason": 99
}'
```

## 4. 预期结果

### 成功响应
```json
{
    "code": 1,
    "msg": "手动退票成功" 或 "手动改签成功",
    "data": {
        "refund_order_id": 123 或 "rebook_order_id": 456
    }
}
```

### 错误响应
```json
{
    "code": 0,
    "msg": "具体错误信息",
    "data": null
}
```

## 5. 数据库验证

测试成功后，可以检查以下数据表：

### 退票相关表
- `ticket_refund_orders` - 退票订单主表
- `ticket_refund_pax` - 退票乘客表
- `ticket_refund_seg` - 退票航段表
- `ticket_refund_order_detail` - 退票订单明细表

### 改签相关表
- `ticket_rebook_orders` - 改签订单主表
- `ticket_rebook_pax` - 改签乘客表
- `ticket_rebook_seg` - 改签航段表
- `ticket_rebook_order_detail` - 改签订单明细表
- `pnr` - 新PNR信息表

## 6. 注意事项

1. 测试前请确保原订单数据存在
2. 航班号和机场代码需要在相应的基础数据表中存在
3. 测试时请使用真实的数据格式
4. 注意检查生成的订单号格式（R开头为退票，C开头为改签）
5. 验证数据库中的数据完整性和一致性
