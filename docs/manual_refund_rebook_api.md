# 手动退票和改签接口文档

## 概述

本文档描述了手动退票和手动改签接口的使用方法。这两个接口用于手动录入退票和改签信息，不调用IBE接口，直接写入数据库。

## 1. 手动退票接口

### 接口信息
- **接口地址**: `POST /admin/order/manual/refund`
- **接口功能**: 用于手动录入退票信息
- **Content-Type**: `application/json`

### 请求参数

#### 原订单信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| origin_order_id | int | 是 | 原订单ID |
| origin_order_no | string | 是 | 原订单号，最大20位 |
| origin_pnr | string | 是 | 原PNR，6位 |
| area_type | int | 是 | 区域类型：1国内 2国际 |

#### 退票乘客信息 (passengers)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| person_name | string | 是 | 乘客姓名，最大50位 |
| doc_id | string | 是 | 证件号，最大30位 |
| telephone | string | 是 | 手机号，最大20位 |
| passenger_type | int | 是 | 乘客类型：1成人 2儿童 3婴儿 |
| ticket_number | string | 是 | 票号，最大20位 |
| original_price | decimal | 是 | 原票价 |
| refund_amount | decimal | 是 | 退款金额 |
| refund_fee | decimal | 是 | 退票费 |
| service_fee | decimal | 否 | 手续费 |

#### 退票航段信息 (segments)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flight_number | string | 是 | 航班号，6-10位 |
| departure_datetime | string | 是 | 出发时间，格式：Y-m-d H:i:s |
| departure_airport | string | 是 | 出发机场代码，3位 |
| arrival_datetime | string | 是 | 到达时间，格式：Y-m-d H:i:s |
| arrival_airport | string | 是 | 到达机场代码，3位 |
| cabin | string | 是 | 舱位，最大5位 |

#### 联系人信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contact_name | string | 是 | 联系人姓名，最大50位 |
| contact_telephone | string | 是 | 联系人手机，最大20位 |
| contact_email | string | 否 | 联系人邮箱，最大100位 |

#### 退票信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| refund_reason | int | 是 | 退票原因：1自愿退票 2被动退票 3其他 4系统退票 |
| remark | string | 否 | 备注，最大200位 |

### 请求示例

```json
{
    "origin_order_id": 12345,
    "origin_order_no": "T202501020001",
    "origin_pnr": "ABC123",
    "area_type": 1,
    "passengers": [
        {
            "person_name": "张三",
            "doc_id": "110101199001011234",
            "telephone": "13800138000",
            "passenger_type": 1,
            "ticket_number": "999-1234567890",
            "original_price": 1000.00,
            "refund_amount": 800.00,
            "refund_fee": 200.00,
            "service_fee": 50.00
        }
    ],
    "segments": [
        {
            "flight_number": "CZ3101",
            "departure_datetime": "2025-01-15 08:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 10:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "contact_name": "张三",
    "contact_telephone": "13800138000",
    "contact_email": "<EMAIL>",
    "refund_reason": 1,
    "remark": "自愿退票"
}
```

### 响应示例

```json
{
    "code": 1,
    "msg": "手动退票成功",
    "data": {
        "refund_order_id": 67890
    }
}
```

## 2. 手动改签接口

### 接口信息
- **接口地址**: `POST /admin/order/manual/rebook`
- **接口功能**: 用于手动录入改签信息
- **Content-Type**: `application/json`

### 请求参数

#### 原订单信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| origin_order_id | int | 是 | 原订单ID |
| origin_order_no | string | 是 | 原订单号，最大20位 |
| origin_pnr | string | 是 | 原PNR，6位 |
| area_type | int | 是 | 区域类型：1国内 2国际 |

#### 改签乘客信息 (passengers)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| person_name | string | 是 | 乘客姓名，最大50位 |
| doc_id | string | 是 | 证件号，最大30位 |
| telephone | string | 是 | 手机号，最大20位 |
| passenger_type | int | 是 | 乘客类型：1成人 2儿童 3婴儿 |
| old_ticket_number | string | 是 | 原票号，最大20位 |
| new_ticket_number | string | 是 | 新票号，最大20位 |
| original_price | decimal | 是 | 原票价 |
| new_price | decimal | 是 | 新票价 |
| price_difference | decimal | 是 | 差价 |
| change_fee | decimal | 是 | 改签费 |
| service_fee | decimal | 否 | 手续费 |

#### 原航段信息 (old_segments)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flight_number | string | 是 | 航班号，6-10位 |
| departure_datetime | string | 是 | 出发时间，格式：Y-m-d H:i:s |
| departure_airport | string | 是 | 出发机场代码，3位 |
| arrival_datetime | string | 是 | 到达时间，格式：Y-m-d H:i:s |
| arrival_airport | string | 是 | 到达机场代码，3位 |
| cabin | string | 是 | 舱位，最大5位 |

#### 新航段信息 (new_segments)
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| flight_number | string | 是 | 航班号，6-10位 |
| departure_datetime | string | 是 | 出发时间，格式：Y-m-d H:i:s |
| departure_airport | string | 是 | 出发机场代码，3位 |
| arrival_datetime | string | 是 | 到达时间，格式：Y-m-d H:i:s |
| arrival_airport | string | 是 | 到达机场代码，3位 |
| cabin | string | 是 | 舱位，最大5位 |

#### 联系人信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| contact_name | string | 是 | 联系人姓名，最大50位 |
| contact_telephone | string | 是 | 联系人手机，最大20位 |
| contact_email | string | 否 | 联系人邮箱，最大100位 |

#### 改签信息
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| rebook_reason | int | 是 | 改签原因：1自愿改签 2被动改签 3其他 4系统改签 |
| remark | string | 否 | 备注，最大200位 |
| new_pnr | string | 否 | 新PNR，6位，不提供则自动生成 |

### 请求示例

```json
{
    "origin_order_id": 12345,
    "origin_order_no": "T202501020001",
    "origin_pnr": "ABC123",
    "area_type": 1,
    "passengers": [
        {
            "person_name": "张三",
            "doc_id": "110101199001011234",
            "telephone": "13800138000",
            "passenger_type": 1,
            "old_ticket_number": "999-1234567890",
            "new_ticket_number": "999-1234567891",
            "original_price": 1000.00,
            "new_price": 1200.00,
            "price_difference": 200.00,
            "change_fee": 100.00,
            "service_fee": 50.00
        }
    ],
    "old_segments": [
        {
            "flight_number": "CZ3101",
            "departure_datetime": "2025-01-15 08:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 10:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "new_segments": [
        {
            "flight_number": "CZ3103",
            "departure_datetime": "2025-01-15 14:00:00",
            "departure_airport": "CAN",
            "arrival_datetime": "2025-01-15 16:30:00",
            "arrival_airport": "PEK",
            "cabin": "Y"
        }
    ],
    "contact_name": "张三",
    "contact_telephone": "13800138000",
    "contact_email": "<EMAIL>",
    "rebook_reason": 1,
    "remark": "自愿改签",
    "new_pnr": "DEF456"
}
```

### 响应示例

```json
{
    "code": 1,
    "msg": "手动改签成功",
    "data": {
        "rebook_order_id": 67891
    }
}
```

## 错误响应

当请求失败时，返回格式如下：

```json
{
    "code": 0,
    "msg": "错误信息",
    "data": null
}
```

## 注意事项

1. 这两个接口都不会调用IBE接口，只是将数据直接写入数据库
2. 原订单信息必须在数据库中存在且匹配
3. 航段和乘客信息会进行基本的数据验证
4. 退票订单号以 'R' 开头，改签订单号以 'C' 开头
5. 所有金额字段支持小数
6. 时间格式必须为 'Y-m-d H:i:s'
7. 机场代码必须为3位大写字母
8. 航班号长度为6-10位
