<?php

namespace App\Services\Order;

use App\Helpers\Tools\Idcard;
use App\Services\BaseService;

class Manual extends BaseService
{
    /**
     * 处理手动出票业务逻辑
     *
     * @param  array  $data
     *
     * @return int
     * @throws \Exception
     */
    public function processManualIssue(array $data): int
    {
        $db = db_connect();

        // 加载模型
        $ticketBookOrderModel       = model('TicketBookOrderModel');
        $ticketBookPaxModel         = model('TicketBookPaxModel');
        $ticketBookSegModel         = model('TicketBookSegModel');
        $ticketBookPaxSegModel      = model('TicketBookPaxSegModel');
        $ticketBookOrderDetailModel = model('TicketBookOrderDetailModel');
        $flightModel                = model('FlightModel');
        $airportModel               = model('AirportModel');
        $pnrModel                   = model('PnrModel');

        try {
            $db->transException(true)->transStart();

            // 1. 数据查询和验证
            $this->validateAndEnrichData($data, $flightModel, $airportModel);

            // 2. 生成订单号和PNR
            $orderNo = $ticketBookOrderModel->generate_order_no('T');
            $pnr     = $data['pnr'] ?? $this->generatePnr();

            // 3. 计算订单基本信息
            $orderInfo = $this->calculateOrderInfo($data, $orderNo, $pnr);

            // 4. 保存PNR信息
            $pnrId = $this->savePnrInfo($pnrModel, $pnr, $data);

            // 5. 保存订单主表
            $orderId = $this->saveMainOrder($ticketBookOrderModel, $orderInfo, $pnrId);

            // 5.1 回写pnr表的order_id
            $pnrModel->where('id', $pnrId)->set('order_id', $orderId)->update();

            // 6. 保存乘客信息
            $passengerIds = $this->savePassengers($ticketBookPaxModel, $orderId, $data['passengers'], $data['area_type']);

            // 7. 保存航段信息
            $segmentIds = $this->saveSegments($ticketBookSegModel, $orderId, $data['segments'], count($data['passengers']));

            // 8. 保存乘客-航段关联信息
            $this->savePassengerSegments($ticketBookPaxSegModel, $passengerIds, $segmentIds, $data);

            // 9. 保存订单明细信息
            $this->saveOrderDetails($ticketBookOrderDetailModel, $orderId, $passengerIds, $data['passengers']);

            $db->transComplete();

            return $orderId;
        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * 验证并补全数据
     *
     * @param  array  $data
     * @param  object  $flightModel
     * @param  object  $airportModel
     *
     * @return void
     * @throws \Exception
     */
    private function validateAndEnrichData(array &$data, $flightModel, $airportModel): void
    {
        // 验证航班信息并补全数据
        $flightNumbers = array_column($data['segments'], 'flight_number');
        $flights       = $flightModel->whereIn('flight_number', $flightNumbers)->findAll();
        $flightMap     = array_column($flights, null, 'flight_number');

        // 获取机场信息
        $airportCodes = [];
        foreach ($data['segments'] as $segment) {
            $airportCodes[] = $segment['departure_airport'];
            $airportCodes[] = $segment['arrival_airport'];
        }
        $airports   = $airportModel->whereIn('airport_code', array_unique($airportCodes))->findAll();
        $airportMap = array_column($airports, null, 'airport_code');

        // 验证并补全航段信息
        foreach ($data['segments'] as &$segment) {
            $flightNumber = $segment['flight_number'];

            // 如果数据库中有航班信息，使用数据库数据补全
            if (isset($flightMap[$flightNumber])) {
                $flight                  = $flightMap[$flightNumber];
                $segment['airline_code'] = $flight['airline_code'];

                // 验证机场代码是否匹配
                if (!empty($flight['departure_airport']) && $flight['departure_airport'] !== $segment['departure_airport']) {
                    throw new \Exception("航班 {$flightNumber} 的出发机场不匹配");
                }
                if (!empty($flight['arrival_airport']) && $flight['arrival_airport'] !== $segment['arrival_airport']) {
                    throw new \Exception("航班 {$flightNumber} 的到达机场不匹配");
                }
            } else {
                // 从航班号提取航司代码
                $segment['airline_code'] = substr($flightNumber, 0, 2);
            }

            // 验证机场代码
            if (!isset($airportMap[$segment['departure_airport']])) {
                throw new \Exception("未知的出发机场代码: {$segment['departure_airport']}");
            }
            if (!isset($airportMap[$segment['arrival_airport']])) {
                throw new \Exception("未知的到达机场代码: {$segment['arrival_airport']}");
            }

            // 如果出发机场和到达机场，其中有一个是国际机场，则航线类型为国际
            if ($airportMap[$segment['departure_airport']]['type'] == 2 || $airportMap[$segment['arrival_airport']]['type'] == 2) {
                $data['area_type'] = 2;
            }
        }

        // 验证乘客信息
        $totalSupplierAmount = 0.00;
        $totalCustomerAmount = 0.00;
        foreach ($data['passengers'] as $key => $passenger) {
            if (empty($passenger['person_name'])) {
                throw new \Exception("乘客姓名不能为空");
            }
            if (empty($passenger['doc_id'])) {
                throw new \Exception("乘客证件号不能为空");
            }

            // 计算总金额
            $supplierAmount   = 0.00;
            $customerAmount   = 0.00;
            $ticketTotalPrice = 0.00;

            // 票面总价=票面价+机场建设费+燃油附加费
            $ticketTotalPrice = bcadd($ticketTotalPrice, $passenger['ticket_price'], 2);
            if ($data['area_type'] == 1) {
                // 国内加机建燃油
                $ticketTotalPrice = bcadd($ticketTotalPrice, $passenger['tax_cn'], 2);
                $ticketTotalPrice = bcadd($ticketTotalPrice, $passenger['tax_yq'], 2);
            } else {
                // 国际统一为税费
                $ticketTotalPrice = bcadd($ticketTotalPrice, $passenger['tax_xt'], 2);
            }

            // 采购总价=票面总价-代理费+采购服务费+保险
            $supplierAmount = bcadd($supplierAmount, $ticketTotalPrice, 2);
            $supplierAmount = bcsub($supplierAmount, $passenger['agency_fee'], 2);
            $supplierAmount = bcadd($supplierAmount, $passenger['supplier_service_fee'], 2);
            $supplierAmount = bcadd($supplierAmount, $passenger['insurance_fee'], 2);

            // 销售总价=票面总价-让利价+销售服务费+保险
            $customerAmount = bcadd($customerAmount, $ticketTotalPrice, 2);
            $customerAmount = bcsub($customerAmount, $passenger['adjust_fee'], 2);
            $customerAmount = bcadd($customerAmount, $passenger['customer_service_fee'], 2);
            $customerAmount = bcadd($customerAmount, $passenger['insurance_fee'], 2);

            $data['passengers'][$key]['ticket_total_price'] = $ticketTotalPrice;
            $data['passengers'][$key]['supplier_amount']    = $supplierAmount;
            $data['passengers'][$key]['customer_amount']    = $customerAmount;

            $totalSupplierAmount = bcadd($totalSupplierAmount, $supplierAmount, 2);
            $totalCustomerAmount = bcadd($totalCustomerAmount, $customerAmount, 2);
        }

        $data['total_supplier_amount'] = $totalSupplierAmount;
        $data['total_customer_amount'] = $totalCustomerAmount;
    }

    /**
     * 生成PNR
     *
     * @return string
     */
    private function generatePnr(): string
    {
        // 生成6位随机PNR
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $pnr        = '';
        for ($i = 0; $i < 6; $i++) {
            $pnr .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $pnr;
    }

    /**
     * 计算订单基本信息
     *
     * @param  array  $data
     * @param  string  $orderNo
     * @param  string  $pnr
     *
     * @return array
     */
    private function calculateOrderInfo(array $data, string $orderNo, string $pnr): array
    {
        $passengerCount = count($data['passengers']);
        $segmentCount   = count($data['segments']);

        // 计算航程类型
        $journeyType = 1; // 默认单程
        if ($segmentCount == 2) {
            // 检查是否为往返
            $firstSeg  = $data['segments'][0];
            $secondSeg = $data['segments'][1];
            if ($firstSeg['departure_airport'] === $secondSeg['arrival_airport'] &&
                $firstSeg['arrival_airport'] === $secondSeg['departure_airport']) {
                $journeyType = 2; // 往返
            } else {
                $journeyType = 3; // 联程-两航段
            }
        } elseif ($segmentCount > 2) {
            $journeyType = 5; // 多航段
        }

        // 构建航程信息
        $journeyInfo = '';
        foreach ($data['segments'] as $segment) {
            $journeyInfo .= $segment['departure_airport'] . $segment['arrival_airport'];
        }

        // 国内 or 国际
        $areaType = $data['area_type'];

        // 构建乘客姓名
        $passengerNames = implode(',', array_column($data['passengers'], 'person_name'));

        return [
            'order_no'              => $orderNo,
            'pnr'                   => $pnr,
            'journey_type'          => $journeyType,
            'journey_info'          => $journeyInfo,
            'passenger_number'      => $passengerCount,
            'passenger_names'       => $passengerNames,
            'contact_name'          => $data['contact_name'],
            'contact_telephone'     => $data['contact_telephone'],
            'contact_email'         => $data['contact_email'] ?? '',
            'area_type'             => $areaType,
            'total_supplier_amount' => $data['total_supplier_amount'],
            'total_customer_amount' => $data['total_customer_amount'],
        ];
    }

    /**
     * 保存PNR信息
     *
     * @param  object  $pnrModel
     * @param  string  $pnr
     * @param  array  $data
     *
     * @return int
     */
    private function savePnrInfo($pnrModel, string $pnr, array $data): int
    {
        return $pnrModel->insert([
            'pnr'               => $pnr,
            'passenger_number'  => count($data['passengers']),
            'contact_name'      => $data['contact_name'],
            'contact_telephone' => $data['contact_telephone'],
            'status'            => 0,
            'created_at'        => time(),
            'updated_at'        => time(),
        ]);
    }

    /**
     * 保存订单主表
     *
     * @param  object  $ticketBookOrderModel
     * @param  array  $orderInfo
     * @param  int  $pnrId
     *
     * @return int
     */
    private function saveMainOrder($ticketBookOrderModel, array $orderInfo, int $pnrId): int
    {
        return $ticketBookOrderModel->insert([
            'order_no'              => $orderInfo['order_no'],
            'order_type'            => 1, // 出票订单
            'ticket_type'           => 3, // 1.BSP 2.GP 3.OP
            'bsp_payment_type'      => 0,
            'order_source'          => 2, // 手工订单
            'area_type'             => $orderInfo['area_type'],
            'customer_type'         => 1, // 自有客户
            'customer_id'           => 0,
            'pnr'                   => $orderInfo['pnr'],
            'pnr_id'                => $pnrId,
            'journey_type'          => $orderInfo['journey_type'],
            'journey_info'          => $orderInfo['journey_info'],
            'passenger_number'      => $orderInfo['passenger_number'],
            'passenger_names'       => $orderInfo['passenger_names'],
            'contact_name'          => $orderInfo['contact_name'],
            'contact_telephone'     => $orderInfo['contact_telephone'],
            'contact_email'         => $orderInfo['contact_email'],
            'total_supplier_amount' => $orderInfo['total_supplier_amount'],
            'total_customer_amount' => $orderInfo['total_customer_amount'],
            'office'                => config('IBE')->office ?? '',
            'status'                => 2, // 已出票
            'customer_payment_flag' => 2, // 已付款
            'operator_id'           => 1, // TODO: 获取当前用户ID
            'operator_name'         => '手动录入', // TODO: 获取当前用户名
            'created_at'            => time(),
        ]);
    }

    /**
     * 保存乘客信息
     *
     * @param  object  $ticketBookPaxModel
     * @param  int  $orderId
     * @param  array  $passengers
     * @param  int  $areaType
     *
     * @return array
     */
    private function savePassengers(object $ticketBookPaxModel, int $orderId, array $passengers, int $areaType = 1): array
    {
        $passengerIds = [];
        foreach ($passengers as $key => $passenger) {
            if ($areaType == 1) {
                $age      = Idcard::get_age($passenger['doc_id']);
                $gender   = Idcard::get_sex($passenger['doc_id']);
                $birthday = Idcard::getBirthday($passenger['doc_id']);
            }

            $passengerId = $ticketBookPaxModel->insert([
                'order_id'         => $orderId,
                'rph'              => $key + 1,
                'pnr_passenger_id' => 0,
                'passenger_type'   => $passenger['passenger_type'],
                'ticket_number'    => $passenger['ticket_number'] ?? '',
                'total_amount'     => $passenger['ticket_price'],
                'currency_code'    => 'CNY',
                'doc_id'           => $passenger['doc_id'],
                'person_name'      => $passenger['person_name'],
                'name_prn'         => $passenger['person_name'],
                'passenger_age'    => $age ?? 0,
                'gender'           => $gender ?? 0,
                'birthday'         => $birthday ?? '',
                'telephone'        => $passenger['telephone'],
                'payment_type'     => 'CASH',
                'status'           => 1,
                'created_at'       => time(),
                'updated_at'       => time(),
            ]);

            $passengerIds[] = $passengerId;
        }

        return $passengerIds;
    }

    /**
     * 保存航段信息
     *
     * @param  object  $ticketBookSegModel
     * @param  int  $orderId
     * @param  array  $segments
     * @param  int  $passengerNumber
     *
     * @return array
     */
    private function saveSegments($ticketBookSegModel, int $orderId, array $segments, int $passengerNumber): array
    {
        $segmentIds = [];

        foreach ($segments as $index => $segment) {
            $segmentId = $ticketBookSegModel->insert([
                'order_id'                => $orderId,
                'rph'                     => $index + 1,
                'departure_datetime'      => $segment['departure_datetime'],
                'arrival_datetime'        => $segment['arrival_datetime'],
                'airline'                 => $segment['airline_code'],
                'flight_number'           => $segment['flight_number'],
                'operating_airline'       => $segment['airline_code'],
                'operating_flight_number' => $segment['flight_number'],
                'cabin'                   => $segment['cabin'],
                'sub_cabin'               => $segment['cabin'],
                'passenger_number'        => $passengerNumber, // 每个航段的乘客数量
                'created_at'              => time(),
                'updated_at'              => time(),
            ]);

            $segmentIds[] = $segmentId;
        }

        return $segmentIds;
    }

    /**
     * 保存乘客-航段关联信息
     *
     * @param  object  $ticketBookPaxSegModel
     * @param  array  $passengerIds
     * @param  array  $segmentIds
     * @param  array  $data
     *
     * @return void
     */
    private function savePassengerSegments($ticketBookPaxSegModel, array $passengerIds, array $segmentIds, array $data): void
    {
        foreach ($passengerIds as $passengerIndex => $passengerId) {
            $passenger = $data['passengers'][$passengerIndex];

            foreach ($segmentIds as $segmentIndex => $segmentId) {
                $segment = $data['segments'][$segmentIndex];

                $ticketBookPaxSegModel->insert([
                    'passenger_id'       => $passengerId,
                    'ticket_number'      => $passenger['ticket_number'] ?? '',
                    'flight_number'      => $segment['flight_number'],
                    'departure_datetime' => $segment['departure_datetime'],
                    'departure_airport'  => $segment['departure_airport'],
                    'arrival_airport'    => $segment['arrival_airport'],
                    'marketing_airline'  => $segment['airline_code'],
                    'ticket_status'      => 'OPEN FOR USE',
                    'status'             => 1,
                    'created_at'         => time(),
                    'updated_at'         => time(),
                ]);
            }
        }
    }

    /**
     * 保存订单明细信息
     *
     * @param  object  $ticketBookOrderDetailModel
     * @param  int  $orderId
     * @param  array  $passengerIds
     * @param  array  $passengers
     *
     * @return void
     */
    private function saveOrderDetails($ticketBookOrderDetailModel, int $orderId, array $passengerIds, array $passengers): void
    {
        foreach ($passengerIds as $index => $passengerId) {
            $passenger = $passengers[$index];

            $ticketBookOrderDetailModel->insert([
                'order_id'                      => $orderId,
                'order_passenger_id'            => $passengerId,
                'product_type'                  => 1, // 机票
                'product_id'                    => 0,
                'supplier_id'                   => 0,
                'customer_id'                   => 0,
                'supplier_amount'               => $passenger['supplier_amount'],
                'customer_amount'               => $passenger['customer_amount'],
                'ticket_marketing_price'        => $passenger['ticket_price'],
                'ticket_tax_cn'                 => $passenger['tax_cn'] ?? 0,
                'ticket_tax_yq'                 => $passenger['tax_yq'] ?? 0,
                'ticket_tax_xt'                 => $passenger['tax_xt'] ?? 0,
                'ticket_total_price'            => $passenger['ticket_total_price'],
                'ticket_supplier_price'         => $passenger['supplier_amount'],
                'ticket_supplier_agency_fee'    => $passenger['agency_fee'] ?? 0,
                'ticket_supplier_service_fee'   => $passenger['supplier_service_fee'],
                'ticket_customer_adjust_fee'    => $passenger['adjust_fee'],
                'ticket_customer_service_fee'   => $passenger['customer_service_fee'],
                'insurance_marketing_price'     => $passenger['insurance_fee'],
                'insurance_supplier_adjust_fee' => $passenger['insurance_fee'],
                'insurance_customer_adjust_fee' => $passenger['insurance_fee'],
                'origin_tax_cn'                 => $passenger['tax_cn'] ?? 0,
                'origin_tax_yq'                 => $passenger['tax_yq'] ?? 0,
                'origin_tax_xt'                 => $passenger['tax_xt'] ?? 0,
                'created_at'                    => time(),
                'updated_at'                    => time(),
            ]);
        }
    }

    /**
     * 处理手动退票业务逻辑
     *
     * @param  array  $data
     *
     * @return int
     * @throws \Exception
     */
    public function processManualRefund(array $data): int
    {
        $db = db_connect();

        // 加载模型
        $ticketRefundOrderModel       = model('TicketRefundOrderModel');
        $ticketRefundPaxModel         = model('TicketRefundPaxModel');
        $ticketRefundSegModel         = model('TicketRefundSegModel');
        $ticketRefundOrderDetailModel = model('TicketRefundOrderDetailModel');
        $flightModel                  = model('FlightModel');
        $airportModel                 = model('AirportModel');
        $pnrModel                     = model('PnrModel');

        try {
            $db->transException(true)->transStart();

            // 1. 验证原订单信息
            $this->validateOriginOrder($data);

            // 2. 验证并补全航段和乘客数据
            $this->validateAndEnrichRefundData($data, $flightModel, $airportModel);

            // 3. 生成退票订单号
            $refundOrderNo = $ticketRefundOrderModel->generate_order_no('R');

            // 4. 计算退票订单基本信息
            $refundOrderInfo = $this->calculateRefundOrderInfo($data, $refundOrderNo);

            // 5. 保存退票订单主表
            $refundOrderId = $this->saveRefundMainOrder($ticketRefundOrderModel, $refundOrderInfo);

            // 6. 保存退票乘客信息
            $refundPassengerIds = $this->saveRefundPassengers($ticketRefundPaxModel, $refundOrderId, $data['passengers'], $data['area_type']);

            // 7. 保存退票航段信息
            $refundSegmentIds = $this->saveRefundSegments($ticketRefundSegModel, $refundOrderId, $data['segments'], count($data['passengers']));

            // 8. 保存退票订单明细信息
            $this->saveRefundOrderDetails($ticketRefundOrderDetailModel, $refundOrderId, $refundPassengerIds, $data['passengers']);

            $db->transComplete();

            return $refundOrderId;
        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * 处理手动改签业务逻辑
     *
     * @param  array  $data
     *
     * @return int
     * @throws \Exception
     */
    public function processManualRebook(array $data): int
    {
        $db = db_connect();

        // 加载模型
        $ticketRebookOrderModel       = model('TicketRebookOrderModel');
        $ticketRebookPaxModel         = model('TicketRebookPaxModel');
        $ticketRebookSegModel         = model('TicketRebookSegModel');
        $ticketRebookOrderDetailModel = model('TicketRebookOrderDetailModel');
        $flightModel                  = model('FlightModel');
        $airportModel                 = model('AirportModel');
        $pnrModel                     = model('PnrModel');

        try {
            $db->transException(true)->transStart();

            // 1. 验证原订单信息
            $this->validateOriginOrder($data);

            // 2. 验证并补全航段和乘客数据
            $this->validateAndEnrichRebookData($data, $flightModel, $airportModel);

            // 3. 生成改签订单号和新PNR
            $rebookOrderNo = $ticketRebookOrderModel->generate_order_no('C');
            $newPnr = $data['new_pnr'] ?? $this->generatePnr();

            // 4. 计算改签订单基本信息
            $rebookOrderInfo = $this->calculateRebookOrderInfo($data, $rebookOrderNo, $newPnr);

            // 5. 保存新PNR信息
            $newPnrId = $this->saveRebookPnrInfo($pnrModel, $newPnr, $data);

            // 6. 保存改签订单主表
            $rebookOrderId = $this->saveRebookMainOrder($ticketRebookOrderModel, $rebookOrderInfo, $newPnrId);

            // 6.1 回写pnr表的order_id
            $pnrModel->where('id', $newPnrId)->set('order_id', $rebookOrderId)->update();

            // 7. 保存改签乘客信息
            $rebookPassengerIds = $this->saveRebookPassengers($ticketRebookPaxModel, $rebookOrderId, $data['passengers'], $data['area_type']);

            // 8. 保存原航段和新航段信息
            $oldSegmentIds = $this->saveRebookSegments($ticketRebookSegModel, $rebookOrderId, $data['old_segments'], count($data['passengers']), 'old');
            $newSegmentIds = $this->saveRebookSegments($ticketRebookSegModel, $rebookOrderId, $data['new_segments'], count($data['passengers']), 'new');

            // 9. 保存改签订单明细信息
            $this->saveRebookOrderDetails($ticketRebookOrderDetailModel, $rebookOrderId, $rebookPassengerIds, $data['passengers']);

            $db->transComplete();

            return $rebookOrderId;
        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * 验证原订单信息
     *
     * @param  array  $data
     *
     * @throws \Exception
     */
    private function validateOriginOrder(array $data): void
    {
        $ticketBookOrderModel = model('TicketBookOrderModel');
        $originOrder = $ticketBookOrderModel->find($data['origin_order_id']);

        if (empty($originOrder)) {
            throw new \Exception('原订单不存在');
        }

        if ($originOrder['order_no'] !== $data['origin_order_no']) {
            throw new \Exception('原订单号不匹配');
        }

        if ($originOrder['pnr'] !== $data['origin_pnr']) {
            throw new \Exception('原PNR不匹配');
        }
    }

    /**
     * 验证并补全退票数据
     *
     * @param  array  $data
     * @param  object  $flightModel
     * @param  object  $airportModel
     *
     * @throws \Exception
     */
    private function validateAndEnrichRefundData(array &$data, $flightModel, $airportModel): void
    {
        // 验证航段信息
        foreach ($data['segments'] as &$segment) {
            $this->validateAndEnrichSegment($segment, $flightModel, $airportModel);
        }

        // 验证乘客信息
        foreach ($data['passengers'] as &$passenger) {
            $this->validatePassengerData($passenger);
        }
    }

    /**
     * 验证并补全改签数据
     *
     * @param  array  $data
     * @param  object  $flightModel
     * @param  object  $airportModel
     *
     * @throws \Exception
     */
    private function validateAndEnrichRebookData(array &$data, $flightModel, $airportModel): void
    {
        // 验证原航段信息
        foreach ($data['old_segments'] as &$segment) {
            $this->validateAndEnrichSegment($segment, $flightModel, $airportModel);
        }

        // 验证新航段信息
        foreach ($data['new_segments'] as &$segment) {
            $this->validateAndEnrichSegment($segment, $flightModel, $airportModel);
        }

        // 验证乘客信息
        foreach ($data['passengers'] as &$passenger) {
            $this->validatePassengerData($passenger);
        }
    }

    /**
     * 计算退票订单基本信息
     *
     * @param  array  $data
     * @param  string  $refundOrderNo
     *
     * @return array
     */
    private function calculateRefundOrderInfo(array $data, string $refundOrderNo): array
    {
        $totalRefundAmount = 0;
        $totalRefundFee = 0;
        $totalServiceFee = 0;
        $passengerNames = [];

        foreach ($data['passengers'] as $passenger) {
            $totalRefundAmount += $passenger['refund_amount'];
            $totalRefundFee += $passenger['refund_fee'];
            $totalServiceFee += $passenger['service_fee'] ?? 0;
            $passengerNames[] = $passenger['person_name'];
        }

        // 构建航程信息
        $journeyInfo = '';
        foreach ($data['segments'] as $segment) {
            $journeyInfo .= $segment['departure_airport'] . $segment['arrival_airport'];
        }

        return [
            'order_no'                  => $refundOrderNo,
            'origin_order_id'           => $data['origin_order_id'],
            'origin_order_no'           => $data['origin_order_no'],
            'origin_pnr'                => $data['origin_pnr'],
            'pnr'                       => $data['origin_pnr'], // 退票使用原PNR
            'area_type'                 => $data['area_type'],
            'journey_info'              => $journeyInfo,
            'passenger_number'          => count($data['passengers']),
            'passenger_names'           => implode(',', $passengerNames),
            'contact_name'              => $data['contact_name'],
            'contact_telephone'         => $data['contact_telephone'],
            'contact_email'             => $data['contact_email'] ?? '',
            'total_refund_amount'       => $totalRefundAmount,
            'total_refund_fee'          => $totalRefundFee,
            'total_service_fee'         => $totalServiceFee,
            'refund_reason'             => $data['refund_reason'],
            'remark'                    => $data['remark'] ?? '',
            'status'                    => 2, // 已退票
            'customer_refund_flag'      => 2, // 已退款
            'operator_id'               => 1, // TODO: 获取当前用户ID
            'operator_name'             => '手动录入', // TODO: 获取当前用户名
            'refunded_at'               => time(),
            'created_at'                => time(),
        ];
    }

    /**
     * 计算改签订单基本信息
     *
     * @param  array  $data
     * @param  string  $rebookOrderNo
     * @param  string  $newPnr
     *
     * @return array
     */
    private function calculateRebookOrderInfo(array $data, string $rebookOrderNo, string $newPnr): array
    {
        $totalPriceDifference = 0;
        $totalChangeFee = 0;
        $totalServiceFee = 0;
        $passengerNames = [];

        foreach ($data['passengers'] as $passenger) {
            $totalPriceDifference += $passenger['price_difference'];
            $totalChangeFee += $passenger['change_fee'];
            $totalServiceFee += $passenger['service_fee'] ?? 0;
            $passengerNames[] = $passenger['person_name'];
        }

        // 构建航程信息
        $journeyInfo = '';
        foreach ($data['new_segments'] as $segment) {
            $journeyInfo .= $segment['departure_airport'] . $segment['arrival_airport'];
        }

        return [
            'order_no'                  => $rebookOrderNo,
            'origin_order_id'           => $data['origin_order_id'],
            'origin_order_no'           => $data['origin_order_no'],
            'origin_pnr'                => $data['origin_pnr'],
            'pnr'                       => $newPnr,
            'area_type'                 => $data['area_type'],
            'journey_info'              => $journeyInfo,
            'passenger_number'          => count($data['passengers']),
            'passenger_names'           => implode(',', $passengerNames),
            'contact_name'              => $data['contact_name'],
            'contact_telephone'         => $data['contact_telephone'],
            'contact_email'             => $data['contact_email'] ?? '',
            'total_price_difference'    => $totalPriceDifference,
            'total_change_fee'          => $totalChangeFee,
            'total_service_fee'         => $totalServiceFee,
            'rebook_reason'             => $data['rebook_reason'],
            'remark'                    => $data['remark'] ?? '',
            'status'                    => 2, // 已改签
            'customer_payment_flag'     => 2, // 已付款
            'operator_id'               => 1, // TODO: 获取当前用户ID
            'operator_name'             => '手动录入', // TODO: 获取当前用户名
            'created_at'                => time(),
        ];
    }

    /**
     * 保存退票订单主表
     *
     * @param  object  $ticketRefundOrderModel
     * @param  array  $orderInfo
     *
     * @return int
     * @throws \Exception
     */
    private function saveRefundMainOrder($ticketRefundOrderModel, array $orderInfo): int
    {
        $orderId = $ticketRefundOrderModel->insert($orderInfo);
        if (empty($orderId)) {
            throw new \Exception('保存退票订单失败');
        }
        return $orderId;
    }

    /**
     * 保存改签订单主表
     *
     * @param  object  $ticketRebookOrderModel
     * @param  array  $orderInfo
     * @param  int  $pnrId
     *
     * @return int
     * @throws \Exception
     */
    private function saveRebookMainOrder($ticketRebookOrderModel, array $orderInfo, int $pnrId): int
    {
        $orderInfo['pnr_id'] = $pnrId;
        $orderId = $ticketRebookOrderModel->insert($orderInfo);
        if (empty($orderId)) {
            throw new \Exception('保存改签订单失败');
        }
        return $orderId;
    }

    /**
     * 保存退票乘客信息
     *
     * @param  object  $ticketRefundPaxModel
     * @param  int  $orderId
     * @param  array  $passengers
     * @param  int  $areaType
     *
     * @return array
     * @throws \Exception
     */
    private function saveRefundPassengers($ticketRefundPaxModel, int $orderId, array $passengers, int $areaType): array
    {
        $passengerIds = [];
        foreach ($passengers as $passenger) {
            $passengerData = [
                'order_id'                  => $orderId,
                'person_name'               => $passenger['person_name'],
                'doc_id'                    => $passenger['doc_id'],
                'telephone'                 => $passenger['telephone'],
                'passenger_type'            => $passenger['passenger_type'],
                'ticket_number'             => $passenger['ticket_number'],
                'original_price'            => $passenger['original_price'],
                'refund_amount'             => $passenger['refund_amount'],
                'refund_fee'                => $passenger['refund_fee'],
                'service_fee'               => $passenger['service_fee'] ?? 0,
                'status'                    => 3, // 已退票
                'created_at'                => time(),
                'updated_at'                => time(),
            ];

            $passengerId = $ticketRefundPaxModel->insert($passengerData);
            if (empty($passengerId)) {
                throw new \Exception('保存退票乘客信息失败');
            }
            $passengerIds[] = $passengerId;
        }
        return $passengerIds;
    }

    /**
     * 保存改签乘客信息
     *
     * @param  object  $ticketRebookPaxModel
     * @param  int  $orderId
     * @param  array  $passengers
     * @param  int  $areaType
     *
     * @return array
     * @throws \Exception
     */
    private function saveRebookPassengers($ticketRebookPaxModel, int $orderId, array $passengers, int $areaType): array
    {
        $passengerIds = [];
        foreach ($passengers as $passenger) {
            $passengerData = [
                'order_id'                  => $orderId,
                'person_name'               => $passenger['person_name'],
                'doc_id'                    => $passenger['doc_id'],
                'telephone'                 => $passenger['telephone'],
                'passenger_type'            => $passenger['passenger_type'],
                'old_ticket_number'         => $passenger['old_ticket_number'],
                'new_ticket_number'         => $passenger['new_ticket_number'],
                'original_price'            => $passenger['original_price'],
                'new_price'                 => $passenger['new_price'],
                'price_difference'          => $passenger['price_difference'],
                'change_fee'                => $passenger['change_fee'],
                'service_fee'               => $passenger['service_fee'] ?? 0,
                'status'                    => 2, // 已改签
                'created_at'                => time(),
                'updated_at'                => time(),
            ];

            $passengerId = $ticketRebookPaxModel->insert($passengerData);
            if (empty($passengerId)) {
                throw new \Exception('保存改签乘客信息失败');
            }
            $passengerIds[] = $passengerId;
        }
        return $passengerIds;
    }

    /**
     * 保存退票航段信息
     *
     * @param  object  $ticketRefundSegModel
     * @param  int  $orderId
     * @param  array  $segments
     * @param  int  $passengerCount
     *
     * @return array
     * @throws \Exception
     */
    private function saveRefundSegments($ticketRefundSegModel, int $orderId, array $segments, int $passengerCount): array
    {
        $segmentIds = [];
        foreach ($segments as $index => $segment) {
            $segmentData = [
                'order_id'                  => $orderId,
                'segment_index'             => $index + 1,
                'flight_number'             => $segment['flight_number'],
                'departure_datetime'        => $segment['departure_datetime'],
                'departure_airport'         => $segment['departure_airport'],
                'arrival_datetime'          => $segment['arrival_datetime'],
                'arrival_airport'           => $segment['arrival_airport'],
                'cabin'                     => $segment['cabin'],
                'airline'                   => $segment['airline'] ?? '',
                'passenger_count'           => $passengerCount,
                'created_at'                => time(),
                'updated_at'                => time(),
            ];

            $segmentId = $ticketRefundSegModel->insert($segmentData);
            if (empty($segmentId)) {
                throw new \Exception('保存退票航段信息失败');
            }
            $segmentIds[] = $segmentId;
        }
        return $segmentIds;
    }

    /**
     * 保存改签航段信息
     *
     * @param  object  $ticketRebookSegModel
     * @param  int  $orderId
     * @param  array  $segments
     * @param  int  $passengerCount
     * @param  string  $type  'old' 或 'new'
     *
     * @return array
     * @throws \Exception
     */
    private function saveRebookSegments($ticketRebookSegModel, int $orderId, array $segments, int $passengerCount, string $type): array
    {
        $segmentIds = [];
        foreach ($segments as $index => $segment) {
            $segmentData = [
                'order_id'                  => $orderId,
                'segment_index'             => $index + 1,
                'segment_type'              => $type, // 'old' 或 'new'
                'flight_number'             => $segment['flight_number'],
                'departure_datetime'        => $segment['departure_datetime'],
                'departure_airport'         => $segment['departure_airport'],
                'arrival_datetime'          => $segment['arrival_datetime'],
                'arrival_airport'           => $segment['arrival_airport'],
                'cabin'                     => $segment['cabin'],
                'airline'                   => $segment['airline'] ?? '',
                'passenger_count'           => $passengerCount,
                'created_at'                => time(),
                'updated_at'                => time(),
            ];

            $segmentId = $ticketRebookSegModel->insert($segmentData);
            if (empty($segmentId)) {
                throw new \Exception('保存改签航段信息失败');
            }
            $segmentIds[] = $segmentId;
        }
        return $segmentIds;
    }

    /**
     * 保存退票订单明细信息
     *
     * @param  object  $ticketRefundOrderDetailModel
     * @param  int  $orderId
     * @param  array  $passengerIds
     * @param  array  $passengers
     *
     * @throws \Exception
     */
    private function saveRefundOrderDetails($ticketRefundOrderDetailModel, int $orderId, array $passengerIds, array $passengers): void
    {
        foreach ($passengerIds as $index => $passengerId) {
            $passenger = $passengers[$index];
            $ticketRefundOrderDetailModel->insert([
                'order_id'                  => $orderId,
                'passenger_id'              => $passengerId,
                'person_name'               => $passenger['person_name'],
                'ticket_number'             => $passenger['ticket_number'],
                'original_price'            => $passenger['original_price'],
                'refund_amount'             => $passenger['refund_amount'],
                'refund_fee'                => $passenger['refund_fee'],
                'service_fee'               => $passenger['service_fee'] ?? 0,
                'created_at'                => time(),
                'updated_at'                => time(),
            ]);
        }
    }

    /**
     * 保存改签订单明细信息
     *
     * @param  object  $ticketRebookOrderDetailModel
     * @param  int  $orderId
     * @param  array  $passengerIds
     * @param  array  $passengers
     *
     * @throws \Exception
     */
    private function saveRebookOrderDetails($ticketRebookOrderDetailModel, int $orderId, array $passengerIds, array $passengers): void
    {
        foreach ($passengerIds as $index => $passengerId) {
            $passenger = $passengers[$index];
            $ticketRebookOrderDetailModel->insert([
                'order_id'                  => $orderId,
                'passenger_id'              => $passengerId,
                'person_name'               => $passenger['person_name'],
                'old_ticket_number'         => $passenger['old_ticket_number'],
                'new_ticket_number'         => $passenger['new_ticket_number'],
                'original_price'            => $passenger['original_price'],
                'new_price'                 => $passenger['new_price'],
                'price_difference'          => $passenger['price_difference'],
                'change_fee'                => $passenger['change_fee'],
                'service_fee'               => $passenger['service_fee'] ?? 0,
                'created_at'                => time(),
                'updated_at'                => time(),
            ]);
        }
    }

    /**
     * 保存改签PNR信息
     *
     * @param  object  $pnrModel
     * @param  string  $newPnr
     * @param  array  $data
     *
     * @return int
     * @throws \Exception
     */
    private function saveRebookPnrInfo($pnrModel, string $newPnr, array $data): int
    {
        $pnrData = [
            'pnr'               => $newPnr,
            'passenger_number'  => count($data['passengers']),
            'contact_name'      => $data['contact_name'],
            'contact_telephone' => $data['contact_telephone'],
            'contact_email'     => $data['contact_email'] ?? '',
            'status'            => 1, // 已出票
            'created_at'        => time(),
            'updated_at'        => time(),
        ];

        $pnrId = $pnrModel->insert($pnrData);
        if (empty($pnrId)) {
            throw new \Exception('保存PNR信息失败');
        }
        return $pnrId;
    }

    /**
     * 验证并补全单个航段信息
     *
     * @param array $segment
     * @param object $flightModel
     * @param object $airportModel
     * @throws \Exception
     */
    private function validateAndEnrichSegment(array &$segment, $flightModel, $airportModel): void
    {
        $flightNumber = $segment['flight_number'];
        // 查询航班信息
        $flight = $flightModel->where('flight_number', $flightNumber)->first();
        if ($flight) {
            $segment['airline_code'] = $flight['airline_code'];
            // 验证机场代码是否匹配
            if (!empty($flight['departure_airport']) && $flight['departure_airport'] !== $segment['departure_airport']) {
                throw new \Exception("航班 {$flightNumber} 的出发机场不匹配");
            }
            if (!empty($flight['arrival_airport']) && $flight['arrival_airport'] !== $segment['arrival_airport']) {
                throw new \Exception("航班 {$flightNumber} 的到达机场不匹配");
            }
        } else {
            // 从航班号提取航司代码
            $segment['airline_code'] = substr($flightNumber, 0, 2);
        }
        // 查询机场信息
        $departureAirport = $airportModel->where('airport_code', $segment['departure_airport'])->first();
        $arrivalAirport = $airportModel->where('airport_code', $segment['arrival_airport'])->first();
        if (!$departureAirport) {
            throw new \Exception("未知的出发机场代码: {$segment['departure_airport']}");
        }
        if (!$arrivalAirport) {
            throw new \Exception("未知的到达机场代码: {$segment['arrival_airport']}");
        }
        // 可根据需要补充更多字段
    }

    /**
     * 验证单个乘客信息
     *
     * @param array $passenger
     * @throws \Exception
     */
    private function validatePassengerData(array $passenger): void
    {
        if (empty($passenger['person_name'])) {
            throw new \Exception("乘客姓名不能为空");
        }
        if (empty($passenger['doc_id'])) {
            throw new \Exception("乘客证件号不能为空");
        }
        // 可根据需要补充更多校验
    }
}
