<?php

namespace App\Services\Order;

use App\Models\FileUploadModel;
use App\Models\OrderPaymentModel;
use App\Models\OrderPaymentRefundModel;
use App\Models\TicketVoidOrderModel;
use App\Models\PaymentAddfundModel;
use App\Models\TicketBookOrderModel;
use App\Models\TicketBookPaxModel;

use App\Models\TicketRebookOrderModel;
use App\Models\TicketRefundOrderModel;
use App\Models\TicketRefundOrderDetailModel;
use App\Models\TicketVoidOrderDetailModel;
use App\Models\CustomerAccountModel;
use App\Models\CustomerCashTransactionModel;
use App\Models\CustomerCreditTransactionModel;
use App\Models\CustomerCashSubjectModel;
use App\Models\CustomerCreditSubjectModel;
use App\Models\OrderPaymentRefundTransferModel;
use App\Services\BaseService;

class Payment extends BaseService
{
    public function __constructor()
    {
    }

    /**
     * @desc 设置会员id
     *
     * @param $params
     *
     * @throws \Exception
     *
     * <AUTHOR> 2025-06-24
     */
    public function setCustomerId($params)
    {
        $orderType  = intval($params['order_type']);
        $orderId    = intval($params['order_id']);
        $customerId = intval($params['customer_id']);

        $customerModel = model('CustomerModel');
        $customer      = $customerModel->find($customerId);
        if (empty($customer)) {
            throw new \Exception('customer_id有误');
        }
        try {
            // 开始数据库事务
            $this->db->transException(true)->transStart();
            switch ($orderType) {
                case OrderPaymentModel::ORDER_TYPE_ISSUE:
                    $orderModel          = model('TicketBookOrderModel');
                    $detailModel         = model('TicketBookOrderDetailModel');
                    $customerPaymentFlag = TicketBookOrderModel::CUSTOMER_PAYMENT_FLAG_PAID;
                    break;
                case OrderPaymentModel::ORDER_TYPE_CHANGE:
                    $orderModel          = model('TicketRebookOrderModel');
                    $detailModel         = model('TicketRebookOrderDetailModel');
                    $customerPaymentFlag = TicketRebookOrderModel::CUSTOMER_PAYMENT_FLAG_PAID;
                    break;
                case OrderPaymentModel::ORDER_TYPE_REFUND:
                    $orderModel          = model('TicketRefundOrderModel');
                    $detailModel         = model('TicketRefundOrderDetailModel');
                    $customerPaymentFlag = TicketRefundOrderModel::CUSTOMER_REFUND_FLAG_PAID;
                    break;
            }
            $order = $orderModel->find($orderId);
            if (empty($order)) {
                throw new \Exception('order_id有误');
            }
            if ($order['customer_payment_flag'] == $customerPaymentFlag) {
                throw new \Exception('该订单已付款,设置会员id失败');
            }
            $orderModel->where('id', $orderId)->set(['customer_id' => $customerId, 'customer_type' => $customer['customer_type']])->update();
            $detailModel->where(['order_id' => $orderId])->set('customer_id', $customerId)->update();
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            throw new \Exception($e->getMessage());
        }

        return true;
    }

    /**
     * @desc 订单支付
     *
     * <AUTHOR> 2025-06-24
     */
    public function pay($params)
    {
        $orderType   = intval($params['order_type']);
        $orderId     = intval($params['order_id']);
        $customerId  = intval($params['customer_id']);
        $balance     = floatval($params['balance']);
        $amount      = floatval($params['amount']);
        $paymentType = intval($params['payment_type']);

        $customerModel                  = model('CustomerModel');
        $customerAccountModel           = model('CustomerAccountModel');
        $orderPaymentModel              = model('OrderPaymentModel');
        $paymentAddfundModel            = model('PaymentAddfundModel');
        $customerCashTransactionModel   = model('CustomerCashTransactionModel');
        $customerCreditTransactionModel = model('CustomerCreditTransactionModel');
        $fileUploadModel                = model('FileUploadModel');
        $paymentChannelModel            = model('PaymentChannelModel');

        //校验
        //订单信息
        switch ($orderType) {
            case OrderPaymentModel::ORDER_TYPE_ISSUE:
                $orderModel          = model('TicketBookOrderModel');
                $detailModel         = model('TicketBookOrderDetailModel');
                $passengerModel      = model('TicketBookPaxModel');
                $customerPaymentFlag = TicketBookOrderModel::CUSTOMER_PAYMENT_FLAG_PAID;
                break;
            case OrderPaymentModel::ORDER_TYPE_CHANGE:
                $orderModel          = model('TicketRebookOrderModel');
                $detailModel         = model('TicketRebookOrderDetailModel');
                $passengerModel      = model('TicketRebookPaxModel');
                $customerPaymentFlag = TicketRebookOrderModel::CUSTOMER_PAYMENT_FLAG_PAID;
                break;
        }
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            throw new \Exception('order_id有误');
        }
        if ($order['customer_id'] != $customerId) {
            throw new \Exception('提交的customer_id参数与订单中的customer_id不一致');
        }
        if ($order['customer_payment_flag'] == $customerPaymentFlag) {
            throw new \Exception('该订单已付款,请勿重复支付');
        }
        if (bccomp($order['total_customer_amount'], $amount, 10) !== 0) {
            throw new \Exception("提交上来的金额:{$amount},与订单上的总销售金额：{$order['total_customer_amount']},不一致,支付失败！");
        }
        //会员信息
        $customer = $customerModel->find($customerId);
        if (empty($customer)) {
            throw new \Exception('customer_id有误');
        }
        //账户信息
        $account = $customerAccountModel->where('customer_id', $customerId)->first();
        if (empty($account)) {
            throw new \Exception("未查询到customerAccount信息");
        }
        try {
            $this->db->transException(true)->transStart();
            switch ($paymentType) {
                case OrderPaymentModel::PAYMENT_TYPE_CASH:
                    if ($account['cash_flag'] == CustomerAccountModel::CASH_FLAG_UNOPEN) {
                        throw new \Exception("预存金账户未开通");
                    }
                    if ($account['cash_balance'] <= 0) {
                        throw new \Exception("预存金余额不足,余额剩余: {$account['cash_balance']}");
                    }
                    if (bccomp($account['cash_balance'], $balance, 10) !== 0) {
                        throw new \Exception("预存金余额有变动,库中余额剩余: {$account['cash_balance']}, 提交上来的余额：{$balance},请刷新页面后重试");
                    }
                    $cashBalance = bcsub($account['cash_balance'], $amount, 2);
                    if ($cashBalance < 0) {
                        throw new \Exception("预存金余额:{$account['cash_balance']},不足抵扣本次提交金额:{$amount}, 请充值后重试");
                    }
                    //1.支付记录
                    $insertOrderPaymentId = $orderPaymentModel->insert([
                        'order_type'   => $orderType,
                        'order_id'     => $orderId,
                        'payment_type' => $paymentType,
                        'amount'       => $amount,
                        'user_id'      => 1,//TODO 暂写死
                        'status'       => OrderPaymentModel::STATUS_PAID,
                        'created_date' => date('Y-m-d', time()),
                    ]);
                    //2.扣款
                    $customerAccountModel->where(['customer_id' => $customerId])->set('cash_balance', $cashBalance)->update();
                    //3.流水记录
                    $customerCashTransactionModel->insert([
                        'transaction_no'     => $customerCashTransactionModel->generateTransactionNo(),
                        'customer_id'        => $customerId,
                        'transaction_type'   => CustomerCashTransactionModel::TRANSACTION_TYPE_EXPEND,
                        'subject_id'         => CustomerCashSubjectModel::SUBJECT_TYPE_AIR_PAID,
                        'amount'             => -$amount,
                        'current_balance'    => $cashBalance,
                        'related_order_type' => CustomerCashTransactionModel::RELATED_ORDER_TYPE_ISSUANCE,
                        'related_order_no'   => $order['order_no'],
                        'payment_id'         => $insertOrderPaymentId,
                        'user_id'            => 1,//TODO 暂写死
                    ]);
                    //4.修改订单支付状态
                    $orderModel->where(['id' => $orderId])->set('customer_payment_flag', $customerPaymentFlag)->update();
                    break;
                case OrderPaymentModel::PAYMENT_TYPE_CREDIT:
                    if ($account['credit_flag'] == CustomerAccountModel::CREDIT_FLAG_UNOPEN) {
                        throw new \Exception("授信账户未开通");
                    }
                    $creditBlance = 0;//授信可用额度
                    $creditBlance = bcadd($creditBlance, $account['credit_line'], 2);
                    $creditBlance = bcadd($creditBlance, $account['temp_credit_line'], 2);
                    $creditBlance = bcsub($creditBlance, $account['credit_used'], 2);
                    if ($creditBlance <= 0) {
                        throw new \Exception("授信余额不足,余额剩余: {$creditBlance}");
                    }
                    if (bccomp($creditBlance, $balance, 10) === 0) {
                        throw new \Exception("授信余额有变动,库中余额剩余: {$creditBlance}, 提交上来的余额：{$balance}");
                    }
                    if (bcsub($creditBlance, $amount, 2) < 0) {
                        throw new \Exception("授信余额:{$creditBlance},不足抵扣本次提交金额:{$amount}, 请提升额度后重试");
                    }
                    //1.支付记录
                    $insertOrderPaymentId = $orderPaymentModel->insert([
                        'order_type'   => $orderType,
                        'order_id'     => $orderId,
                        'payment_type' => $paymentType,
                        'amount'       => $amount,
                        'user_id'      => 1,//TODO 暂写死
                        'status'       => OrderPaymentModel::STATUS_PAID,
                    ]);
                    //2.增加使用额度
                    $creditUsed = bcadd($account['credit_used'], $amount, 2);
                    $customerAccountModel->where(['customer_id' => $customerId])->set('credit_used', $creditUsed)->update();
                    //3.流水记录
                    $detail       = $detailModel->where(['order_id' => $orderId])->findAll();
                    $passengerIds = array_column($detail, 'order_passenger_id');
                    $passengers   = $passengerModel->whereIn('id', $passengerIds)->findAll();
                    if (empty($passengers)) {
                        throw new \Exception("passenger表数据不存在");
                    }
                    $passengers = array_column($passengers, null, 'id');
                    foreach ($detail as $item) {
                        $orderPassenger = $passengers[$item['order_passenger_id']] ?? [];
                        $addrCreditTr   = [
                            'transaction_no'     => $customerCreditTransactionModel->generateTransactionNo(),
                            'customer_id'        => $customerId,
                            'transaction_type'   => CustomerCreditTransactionModel::TRANSACTION_TYPE_EXPEND,
                            'subject_id'         => CustomerCreditSubjectModel::SUBJECT_TYPE_AIR_PAID,
                            'amount'             => $item['customer_amount'],
                            'passenger_id'       => $item['order_passenger_id'],
                            'passenger_name'     => $orderPassenger['person_name'] ?? '',
                            'pnr'                => $order['pnr'],
                            'product_type'       => $item['product_type'],
                            'product_id'         => $item['product_id'],
                            'product_desc'       => '',
                            'fee_desc'           => '',
                            'related_order_type' => CustomerCreditTransactionModel::RELATED_ORDER_TYPE_ISSUANCE,
                            'related_order_id'   => $orderId,
                            'related_order_no'   => $order['order_no'],
                            'payment_id'         => $insertOrderPaymentId,
                            'ticket_number'      => $orderPassenger['ticket_number'] ?? '',
                            'status'             => CustomerCreditTransactionModel::STATUS_UNSETTLED,
                            'user_id'            => 1,//TODO 暂写死
                            'created_date'       => date('Y-m-d', time()),
                        ];
                        $customerCreditTransactionModel->insert($addrCreditTr);
                    }
                    $orderModel->where(['id' => $orderId])->set('customer_payment_flag', $customerPaymentFlag)->update();
                    break;
                case OrderPaymentModel::PAYMENT_TYPE_OFFLINE_TURN:
                case OrderPaymentModel::PAYMENT_TYPE_OFFLINE_QRCODE:
                    $allowStatus = [
                        OrderPaymentModel::STATUS_UNPAID,
                        OrderPaymentModel::STATUS_PAID_FAILED,
                    ];
                    //1.支付记录
                    $orderPayment = $orderPaymentModel->where(['order_type' => $orderType, 'order_id' => $orderId])->first();
                    if (empty($orderPayment)) {
                        $paymentId = $orderPaymentModel->insert([
                            'order_type'   => $orderType,
                            'order_id'     => $orderId,
                            'payment_type' => $paymentType,
                            'amount'       => $amount,
                            'user_id'      => 1,//TODO 暂写死
                            'status'       => OrderPaymentModel::STATUS_PAIDING,
                        ]);
                    } else {
                        if (!in_array($orderPayment['status'], $allowStatus)) {
                            throw new \Exception('【未支付】【支付失败】状态才允许重新提交支付申请');
                        }
                        $paymentId = $orderPayment['id'];
                    }
                    //2.预存金申請记录
                    $paymentAddfund = $paymentAddfundModel->where(['order_id' => $orderId, 'order_type' => $orderType, 'status' => PaymentAddfundModel::STATUS_SUBMITTED])->findAll();
                    if (!empty($paymentAddfund)) {
                        throw new \Exception('存在预存金记录未审核，请勿重复提交');
                    }
                    if (empty($params['channel_id']) || empty($params['payment_date']) || empty($params['payment_images'])) {
                        throw new \Exception('channel_id、payment_date、payment_images必填');
                    }
                    $channelId             = intval($params['channel_id']);
                    $paymentDate           = trim($params['payment_date']);//汇款日期
                    $transferAccountNumber = trim($params['transfer_account_number']);
                    $transferAccountName   = trim($params['transfer_account_name']);
                    $qrcodePaymentChannel = intval($params['qrcode_payment_channel']);//付款渠道
                    $qrcodePaymentNickname = trim($params['qrcode_payment_nickname']);//付款账号/付款人名称

                    $paymentImages = $params['payment_images'] ?? [];
                    $paymentChannel = $paymentChannelModel->find($channelId);
                    if (empty($paymentChannel)) {
                        throw new \Exception("非法channel_id");
                    }
                    if ($paymentType == OrderPaymentModel::PAYMENT_TYPE_OFFLINE_TURN) {//线下支付（线下转账）
                        $addPaymentFundType = PaymentAddfundModel::PAYMENT_TYPE_OFFLINE_TRANSFER;
                        if (empty($transferAccountNumber) || empty($transferAccountName)) {
                            throw new \Exception("汇款银行账号、汇款账号名称不允许为空");
                        }
                        $addPayment = [
                            'transfer_account_number' => $transferAccountNumber,
                            'transfer_account_name' => $transferAccountName,
                        ];
                    } elseif ($paymentType == OrderPaymentModel::PAYMENT_TYPE_OFFLINE_QRCODE) {//线下支付（线下二维码收款）
                        $addPaymentFundType = PaymentAddfundModel::PAYMENT_TYPE_OFFLINE_QR_CODE_COLLECTION;
                        if (empty($params['channel_id'])) {
                            throw new \Exception("付款渠道、付款人名称不允许为空");
                        }
                        if (empty($qrcodePaymentChannel) || empty($qrcodePaymentNickname)) {
                            throw new \Exception("付款渠道、付款人名称不允许为空");
                        }
                        $addPayment = [
                            'qrcode_payment_channel' => $qrcodePaymentChannel,
                            'qrcode_payment_nickname' => $qrcodePaymentNickname,
                        ];
                    }
                    $addPayment['customer_id']             = $customerId;
                    $addPayment['business_type']           = PaymentAddfundModel::BUSINESS_TYPE_PAYMENT;
                    $addPayment['payment_type']            = $addPaymentFundType;//线下转账
                    $addPayment['channel_id']              = $channelId;
                    $addPayment['order_type']              = $orderType;
                    $addPayment['order_id']                = $orderId;
                    $addPayment['amount']                  = $amount;
                    $addPayment['payment_date']            = $paymentDate;
                    $addPayment['apply_user_id']           = 1;//TODO 暂写死
                    $addPayment['apply_at']                = time();
                    $addPayment['status']                  = PaymentAddfundModel::STATUS_SUBMITTED;
                    $paymentAddfundId                      = $paymentAddfundModel->insert($addPayment);
                    foreach ($paymentImages as $paymentImage) {
                        $fileUploadModel->insert([
                            'upload_type' => FileUploadModel::UPLOAD_TYPE_RECHARGE,
                            'related_id'  => $paymentAddfundId,
                            'file_type'   => FileUploadModel::FILE_TYPE_IMG,
                            'file_path'   => FileUploadModel::FILE_PATH_PAYMENT,
                            'file_name'   => $paymentImage,
                            'thumbnail'   => 'thumb_' . $paymentImage,
                            'status'      => FileUploadModel::STATUS_ENABLE,
                        ]);
                    }
                    break;
            }
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            throw new \Exception($e->getMessage());
        }

        return true;
    }

    /**
     * @desc 线下付款审核
     *
     * <AUTHOR> 2025-06-26
     */
    public function audit($params)
    {
        $paymentAddfundModel = model('PaymentAddfundModel');
        $orderPaymentModel   = model('OrderPaymentModel');

        $paymentAddfundId = $params['payment_addfund_id'];
        $status           = $params['status'];//审核状态
        $remark           = $params['remark'];//备注

        $paymentAddfund = $paymentAddfundModel->find($paymentAddfundId);
        if (empty($paymentAddfund)) {
            throw new \Exception("payment_addfund_id错误");
        }
        if ($paymentAddfund['status'] != PaymentAddfundModel::STATUS_SUBMITTED) {
            throw new \Exception("请勿重复审核");
        }
        $orderType = $paymentAddfund['order_type'];
        $orderId   = $paymentAddfund['order_id'];

        try {
            $db = \Config\Database::connect();
            $db->transStart();

            //1.修改充值申请表状态  TODO audit_user_id暂写死
            $paymentAddfundModel->where('id', $paymentAddfundId)->set(['status' => $status, 'audit_remark' => $remark, 'audit_user_id' => 1, 'audit_at' => time()])->update();
            //2.修改订单支付记录
            if ($status == PaymentAddfundModel::STATUS_APPROVED) {
                $orderPaymentStatus = OrderPaymentModel::STATUS_PAID;
            } else {
                $orderPaymentStatus = OrderPaymentModel::STATUS_PAID_FAILED;
            }
            $orderPaymentModel->where([
                'order_type' => $orderType,
                'order_id'   => $orderId,
            ])->set(['status' => $orderPaymentStatus, 'audit_user_id' => 1, 'audit_at' => time()])->update();//TODO audit_user_id暂写死
            //3.修改订单支付状态
            switch ($orderType) {
                case PaymentAddfundModel::ORDER_TYPE_ISSUANCE:
                    $orderModel = model('TicketBookOrderModel');
                    if ($status == PaymentAddfundModel::STATUS_APPROVED) {
                        $customerPaymentFlag = TicketBookOrderModel::CUSTOMER_PAYMENT_FLAG_PAID;
                    } else {
                        $customerPaymentFlag = TicketBookOrderModel::CUSTOMER_PAYMENT_FLAG_FAILED;
                    }
                    break;
                case PaymentAddfundModel::ORDER_TYPE_CHANGE:
                    $orderModel = model('TicketRebookOrderModel');
                    if ($status == PaymentAddfundModel::STATUS_APPROVED) {
                        $customerPaymentFlag = TicketRebookOrderModel::CUSTOMER_PAYMENT_FLAG_PAID;
                    } else {
                        $customerPaymentFlag = TicketRebookOrderModel::CUSTOMER_PAYMENT_FLAG_FAILED;
                    }
                    break;
            }
            $order = $orderModel->find($orderId);
            if (empty($order)) {
                throw new \Exception('order_id有误');
            }
            if ($order['customer_payment_flag'] == $customerPaymentFlag) {
                throw new \Exception('该订单已付款,请勿重复支付');
            }
            $orderModel->where(['id' => $orderId])->set('customer_payment_flag', $customerPaymentFlag)->update();

            $db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $db->transRollback();
            error(0, $e->getMessage());
        }

        return true;
    }

    /**
     * @desc 退款（退票/废票）
     *
     * @param $params
     *
     * <AUTHOR> 2025-06-30
     */
    public function refund($params)
    {
        $orderType   = intval($params['order_type']);
        $paymentType = intval($params['payment_type']);//付款类型：1原路返回 2转为预存金 3银行转账退款
        $orderId     = intval($params['order_id']);
        $amount      = floatval($params['amount']);

        $customerAccountModel            = model('CustomerAccountModel');
        $bookOrderModel                  = model('TicketBookOrderModel');
        $bookPaxModel                    = model('TicketBookPaxModel');
        $reBookOrderModel                = model('TicketRebookOrderModel');
        $orderPaymentModel               = model('OrderPaymentModel');
        $orderPaymentRefundModel         = model('OrderPaymentRefundModel');
        $customerCashTransactionModel    = model('CustomerCashTransactionModel');
        $customerCreditTransactionModel  = model('CustomerCreditTransactionModel');
        $orderPaymentRefundTransferModel = model('OrderPaymentRefundTransferModel');

        switch ($orderType) {
            case OrderPaymentRefundModel::ORDER_TYPE_REFUND://退票
                $orderModel       = model('TicketRefundOrderModel');
                $orderDetailModel = model('TicketRefundOrderDetailModel');
                $passengerModel   = model('TicketRefundPaxModel');

                $customerRefundFlag = TicketRefundOrderModel::CUSTOMER_REFUND_FLAG_PAID;
                $order              = $orderModel->find($orderId);
                if (empty($order)) {
                    throw new \Exception('退票order_id有误');
                }
                if ($order['status'] != TicketRefundOrderModel::ORDER_STATUS_REFUNDED) {
                    throw new \Exception('订单状态必须是【已退票】，才允许退款');
                }
                //查询原订单
                switch ($order['origin_order_type']) {
                    case TicketRefundOrderModel::ORIGIN_ORDER_TYPE_ISSUE://出票订单
                        $oriOrder            = $bookOrderModel->where(['id' => $order['origin_order_id']])->first();
                        $oriPaymentOrderType = OrderPaymentModel::ORDER_TYPE_ISSUE;
                        break;
                    case TicketRefundOrderModel::ORIGIN_ORDER_TYPE_CHANGE://改签订单
                        $oriOrder            = $reBookOrderModel->where(['id' => $order['origin_order_id']])->first();
                        $oriPaymentOrderType = OrderPaymentModel::ORDER_TYPE_CHANGE;
                        break;
                }
                break;
            case OrderPaymentRefundModel::ORDER_TYPE_INVALID://废票
                $orderModel       = model('TicketVoidOrderModel');
                $orderDetailModel = model('TicketVoidOrderDetailModel');
                $passengerModel   = model('TicketRefundPaxModel');

                $customerRefundFlag = TicketVoidOrderModel::CUSTOMER_REFUND_FLAG_PAID;
                $order              = $orderModel->find($orderId);
                if (empty($order)) {
                    throw new \Exception('废票order_id有误');
                }
                if ($order['status'] != TicketVoidOrderModel::ORDER_STATUS_VOIDED) {
                    throw new \Exception('订单状态必须是【已废票】，才允许退款');
                }
                if ($order['origin_order_type'] != TicketVoidOrderModel::ORIGIN_ORDER_TYPE_ISSUE) {
                    throw new \Exception('暂只支持原订单从【出票订单】处进行废票');
                }
                //必须全部都是【已出票】才允许“废票”
                $bookPaxs = $bookPaxModel->where(['order_id' => $orderId])->findAll();
                if (empty($bookPaxs)) {
                    throw new \Exception('bookPax数据有误');
                }
                foreach ($bookPaxs as $bookPax) {
                    if ($bookPax['status'] != TicketBookPaxModel::STATUS_ISSUED) {
                        throw new \Exception('TicketBookPax必须全部是【已出票】状态，才允许废票！');
                    }
                }
                $oriOrder            = $bookOrderModel->where(['id' => $order['origin_order_id']])->first();
                $oriPaymentOrderType = OrderPaymentModel::ORDER_TYPE_ISSUE;
                break;
        }
        $oriOrderPayment = $orderPaymentModel->where(['order_type' => $oriPaymentOrderType, 'order_id' => $oriOrder['id']])->first();
        if (empty($oriOrderPayment)) {
            throw new \Exception('原订单orderPayment数据不存在');
        }
        if ($oriOrderPayment['status'] != OrderPaymentModel::STATUS_PAID) {
            throw new \Exception('原订单orderPayment的状态必须是【已付款】才允许退款');
        }
        if ($order['customer_refund_flag'] == $customerRefundFlag) {
            throw new \Exception('该订单已退款,请勿重复退款');
        }
        if (bccomp($order['total_customer_amount'], -$amount, 10) !== 0) {
            throw new \Exception("提交上来的金额:-{$amount},与订单上的总销售金额：{$order['total_customer_amount']},不一致,退款失败！");
        }
        $customerId = $order['customer_id'];
        $account    = $customerAccountModel->where('customer_id', $customerId)->first();
        if (empty($account)) {
            throw new \Exception("未查询到customerAccount信息");
        }
        try {
            $this->db->transException(true)->transStart();

            //1.退款
            $oriPaymentType    = $oriOrderPayment['payment_type'];//原订单支付类型：1预存金支付 2授信支付 3线下支付 4在线支付
            $oriOrderType      = $oriOrderPayment['order_type'];//原订单类型：1出票订单 2改签订单 3退款订单
            $oriOrderPaymentId = $oriOrderPayment['id'];//原订单支付ID
            //判断退款类型
            switch ($paymentType) {
                case OrderPaymentRefundModel::PAYMENT_TYPE_RETURN_CAME://原路返回
                    if (!in_array($oriPaymentType, [
                        OrderPaymentModel::PAYMENT_TYPE_CASH,
                        OrderPaymentModel::PAYMENT_TYPE_CREDIT,
                    ])) {
                        throw new \Exception('选择【原支付退款】时，原支付方式必须是【预存金支付】或【授信支付】');
                    }
                    //1.新增退款记录
                    $insertOrderPaymentRefundId = $orderPaymentRefundModel->insert([
                        'order_type'               => $orderType,
                        'order_id'                 => $orderId,
                        'payment_type'             => $paymentType,
                        'origin_payment_type'      => $oriPaymentType,
                        'related_order_payment_id' => $oriOrderPaymentId,
                        'amount'                   => -$amount,
                        'user_id'                  => 1,//TODO 暂写死
                        'status'                   => OrderPaymentRefundModel::STATUS_PAID,
                        'created_date'             => date('Y-m-d', time()),
                    ]);
                    //2.回退预存金支付/授信
                    if ($oriPaymentType == OrderPaymentModel::PAYMENT_TYPE_CASH) {
                        //预存金加款
                        $cashBalance = bcadd($account['cash_balance'], $amount, 2);
                        $customerAccountModel->where(['customer_id' => $customerId])->set('cash_balance', $cashBalance)->update();
                        //预存流水记录
                        $customerCashTransactionModel->insert([
                            'transaction_no'     => $customerCashTransactionModel->generateTransactionNo(),
                            'customer_id'        => $customerId,
                            'transaction_type'   => CustomerCashTransactionModel::TRANSACTION_TYPE_INCOME,
                            'subject_id'         => CustomerCashSubjectModel::SUBJECT_TYPE_AIR_REFUND,
                            'amount'             => $amount,
                            'current_balance'    => $cashBalance,
                            'related_order_type' => $orderType,
                            'related_order_no'   => $order['order_no'],
                            'payment_id'         => $insertOrderPaymentRefundId,
                            'user_id'            => 1,//TODO 暂写死
                            'created_date'       => date('Y-m-d', time()),
                        ]);
                        //回退授信支付
                    } elseif ($oriPaymentType == OrderPaymentModel::PAYMENT_TYPE_CREDIT) {
                        //扣减已使用额度
                        $creditUsed = bcsub($account['credit_used'], $amount, 2);
                        $customerAccountModel->where(['customer_id' => $customerId])->set('credit_used', $creditUsed)->update();
                        //details
                        $detail       = $orderDetailModel->where(['order_id' => $orderId])->findAll();
                        $passengerIds = array_column($detail, 'order_passenger_id');
                        $passengers   = $passengerModel->whereIn('id', $passengerIds)->findAll();
                        if (empty($passengers)) {
                            throw new \Exception("passenger表数据不存在");
                        }
                        $passengers = array_column($passengers, null, 'id');
                        foreach ($detail as $item) {
                            if ($orderType == OrderPaymentRefundModel::ORDER_TYPE_REFUND) {
                                if ($item['product_type'] == TicketRefundOrderDetailModel::PRODUCT_TYPE_AIR) {
                                    $subjectId = CustomerCreditSubjectModel::SUBJECT_TYPE_AIR_REFUND;
                                } elseif ($item['product_type'] == TicketRefundOrderDetailModel::PRODUCT_TYPE_INSURANCE) {
                                    $subjectId = CustomerCreditSubjectModel::SUBJECT_TYPE_INSURANCE_REFUND;
                                }
                            } else {
                                if ($item['product_type'] == TicketVoidOrderDetailModel::PRODUCT_TYPE_AIR) {
                                    $subjectId = CustomerCreditSubjectModel::SUBJECT_TYPE_AIR_REFUND;
                                } elseif ($item['product_type'] == TicketVoidOrderDetailModel::PRODUCT_TYPE_INSURANCE) {
                                    $subjectId = CustomerCreditSubjectModel::SUBJECT_TYPE_INSURANCE_REFUND;
                                }
                            }
                            $orderPassenger = $passengers[$item['order_passenger_id']] ?? [];
                            $addrCreditTr   = [
                                'transaction_no'     => $customerCreditTransactionModel->generateTransactionNo(),
                                'customer_id'        => $customerId,
                                'transaction_type'   => CustomerCreditTransactionModel::TRANSACTION_TYPE_RECOVERY,
                                'subject_id'         => $subjectId,
                                'amount'             => $item['customer_amount'],
                                'passenger_id'       => $item['order_passenger_id'],
                                'passenger_name'     => $orderPassenger['person_name'] ?? '',
                                'pnr'                => $order['pnr'],
                                'product_type'       => $item['product_type'],
                                'product_id'         => $item['product_id'],
                                'product_desc'       => '',
                                'fee_desc'           => '',
                                'related_order_type' => $orderType,
                                'related_order_id'   => $orderId,
                                'related_order_no'   => $order['order_no'],
                                'payment_id'         => $insertOrderPaymentRefundId,
                                'ticket_number'      => $orderPassenger['ticket_number'] ?? '',
                                'status'             => CustomerCreditTransactionModel::STATUS_UNSETTLED,
                                'user_id'            => 1,//TODO 暂写死
                                'created_date'       => date('Y-m-d', time()),
                            ];
                            $customerCreditTransactionModel->insert($addrCreditTr);
                        }
                    }
                    //3.修改订单状态
                    $orderModel->where(['id' => $orderId])->set(['customer_refund_flag' => $customerRefundFlag])->update();
                    break;
                case OrderPaymentRefundModel::PAYMENT_TYPE_CONVERT_CASH://转为预存金
                    if (!in_array($oriPaymentType, [OrderPaymentModel::PAYMENT_TYPE_OFFLINE_TURN, OrderPaymentModel::PAYMENT_TYPE_OFFLINE_QRCODE])) {
                        throw new \Exception('选择【转为预存金】时，原支付方式必须是【线下转账】或 【线下二维码收款】');
                    }
                    //1.新增退款记录
                    $insertOrderPaymentRefundId = $orderPaymentRefundModel->insert([
                        'order_type'               => $orderType,
                        'order_id'                 => $orderId,
                        'payment_type'             => $paymentType,
                        'origin_payment_type'      => $oriPaymentType,
                        'related_order_payment_id' => $oriOrderPaymentId,
                        'amount'                   => -$amount,
                        'user_id'                  => 1,//TODO 暂写死
                        'status'                   => OrderPaymentRefundModel::STATUS_PAID,
                        'created_date'             => date('Y-m-d', time()),
                    ]);
                    //2.预存金加款
                    $cashBalance = bcadd($account['cash_balance'], $amount, 2);
                    $customerAccountModel->where(['customer_id' => $customerId])->set('cash_balance', $cashBalance)->update();
                    //预存流水记录
                    $customerCashTransactionModel->insert([
                        'transaction_no'     => $customerCashTransactionModel->generateTransactionNo(),
                        'customer_id'        => $customerId,
                        'transaction_type'   => CustomerCashTransactionModel::TRANSACTION_TYPE_INCOME,
                        'subject_id'         => CustomerCashSubjectModel::SUBJECT_TYPE_TRANSFER_DEPOSIT,
                        'amount'             => $amount,
                        'current_balance'    => $cashBalance,
                        'related_order_type' => $orderType,
                        'related_order_no'   => $order['order_no'],
                        'payment_id'         => $insertOrderPaymentRefundId,
                        'user_id'            => 1,//TODO 暂写死
                        'created_date'       => date('Y-m-d', time()),
                    ]);
                    //3.修改订单状态
                    $orderModel->where(['id' => $orderId])->set(['customer_refund_flag' => $customerRefundFlag])->update();
                    break;
                case OrderPaymentRefundModel::PAYMENT_TYPE_BANK_TRANSFER_REFUND://银行转账退款
                    if (!in_array($oriPaymentType, [OrderPaymentModel::PAYMENT_TYPE_OFFLINE_TURN, OrderPaymentModel::PAYMENT_TYPE_OFFLINE_QRCODE])) {
                        throw new \Exception('选择【银行转账退款】时，原支付方式必须是【线下转账】或 【线下二维码收款】');
                    }
                    if (empty($params['account_name']) || empty($params['account_number']) || empty($params['bank_id'])) {
                        throw new \Exception('收款人姓名、收款银行卡号、所属银行必填');
                    }
                    //1.新增退款记录
                    $insertOrderPaymentRefundId = $orderPaymentRefundModel->insert([
                        'order_type'               => $orderType,
                        'order_id'                 => $orderId,
                        'payment_type'             => $paymentType,
                        'origin_payment_type'      => $oriPaymentType,
                        'related_order_payment_id' => $oriOrderPaymentId,
                        'amount'                   => -$amount,
                        'user_id'                  => 1,//TODO 暂写死
                        'status'                   => OrderPaymentRefundModel::STATUS_PAIDING,
                        'created_date'             => date('Y-m-d', time()),
                    ]);
                    $accountName                = trim($params['account_name']);
                    $accountNumber              = trim($params['account_number']);
                    $bankId                     = intval($params['bank_id']);
                    //2.新增订单退款-银行转账记录
                    $orderPaymentRefundTransferModel->insert([
                        'customer_id'    => $customerId,
                        'refund_id'      => $insertOrderPaymentRefundId,
                        'amount'         => -$amount,
                        'account_name'   => $accountName,
                        'account_number' => $accountNumber,
                        'bank_id'        => $bankId,
                        'apply_user_id'  => 1,//TODO USER_ID暂写死
                        'apply_at'       => time(),
                        'status'         => OrderPaymentRefundTransferModel::STATUS_SUBMITTED,
                    ]);
                    $orderModel->where(['id' => $orderId])->set(['customer_refund_flag' => 1])->update();
                    break;
            }
            $this->db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $this->db->transRollback();
            throw new \Exception($e->getMessage());
        }
    }
}