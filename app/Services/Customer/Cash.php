<?php
namespace App\Services\Customer;

use App\Models\CustomerAccountModel;
use App\Models\CustomerCashSubjectModel;
use App\Models\CustomerCashTransactionModel;
use App\Models\FileUploadModel;
use App\Models\PaymentAddfundModel;
use App\Services\BaseService;

class Cash extends BaseService
{
    public function __constructor()
    {
    }

    /**
     * @desc 预存金交易列表
     * @param $params
     * @return array
     *
     * <AUTHOR> 2025-06-11
     */
    public function list($params)
    {
        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;
        $customerId = intval($params['customer_id']);
        $subjectId = intval($params['subject_id']);//交易类型ID
        $transactionType = intval($params['transaction_type']);//收支类型
        $createdDate = isset($params['created_date']) && $params['created_date'] ? $params['created_date'] : [];

        $customerCashTransactionModel = model('CustomerCashTransactionModel');
        $customerModel = model('CustomerModel');
        $customerAccountModel = model('CustomerAccountModel');

        $customer = $customerModel->find($customerId);
        if (empty($customer)) {
            throw new \Exception("customer_id有误");
        }
        $customerAccount = $customerAccountModel->where('customer_id', $customerId)->first();
        if (empty($customerAccount)) {
            throw new \Exception("未查询到customerAccount信息");
        }

        $where = [];
        if ($customerId) {
            $where['customer_id'] = $customerId;
        }
        if ($subjectId) {
            $where['subject_id'] = $subjectId;
        }
        if ($transactionType) {
            $where['transaction_type'] = $transactionType;
        }
        if ($createdDate) {
            $where['created_date_from'] = $createdDate[0];
            $where['created_date_to'] = $createdDate[1];
        }
        //分页
        $detail = $customerCashTransactionModel->paginateList($where, $page, $perPage);
        $pager = $customerCashTransactionModel->pager;
        $data = [
            'customer_name' => $customer['customer_name'],//会员名称
            'card_no' => $customer['card_no'],//会员卡号
            'cash_balance' => $customerAccount['cash_balance'],//预存金余额
            'detail' => $detail,//交易记录
            'total' => $pager->getTotal(),//总条数
            'perPage' => $pager->getPerPage(),//每页显示条数
            'pageCount' => $pager->getPageCount(),//总页数
            'currentPage' => $pager->getCurrentPage(),//当前页数
        ];
        return $data;
    }

    /**
     * @desc 预存金充值申请/付款订单待审核列表列表
     *
     * @param $params
     *
     * <AUTHOR> 2025-06-26
     */
    public function listApply($params)
    {
        $page = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;
        $customerId = intval($params['customer_id']);
        $businessType = intval($params['business_type']);//业务类型
        $paymentType = intval($params['payment_type']);//支付方式
        $applyAt = isset($params['apply_at']) && $params['apply_at'] ? $params['apply_at'] : [];

        $paymentAddfundModel = model('PaymentAddfundModel');

        $where = [];
        if ($customerId) {
            $where['customer_id'] = $customerId;
        }
        if ($businessType) {
            $where['business_type'] = $businessType;
        }
        if ($paymentType) {
            $where['payment_type'] = $paymentType;
        }
        if ($applyAt) {
            $where['apply_at_from'] = $applyAt[0];
            $where['apply_at_to'] = $applyAt[1];
        }
        //分页
        $list = $paymentAddfundModel->paginateList($where, $page, $perPage);
        $pager = $paymentAddfundModel->pager;
        $data = [
            'list' => $list,//交易记录
            'total' => $pager->getTotal(),//总条数
            'perPage' => $pager->getPerPage(),//每页显示条数
            'pageCount' => $pager->getPageCount(),//总页数
            'currentPage' => $pager->getCurrentPage(),//当前页数
        ];
        return $data;
    }

    /**
     * @desc 预存金充值申请/付款订单待审核详情
     *
     * @param $params
     *
     * <AUTHOR> 2025-06-26
     */
    public function applyDetail($params)
    {
        $paymentAddfundModel = model('PaymentAddfundModel');
        $paymentChannelModel = model('PaymentChannelModel');
        $userModel = model('UserModel');
        $fileUploadModel = model('FileUploadModel');

        $applyId = intval($params['apply_id']);
        $paymentAddfund = $paymentAddfundModel->find($applyId);
        if (empty($paymentAddfund)) {
            error(0, 'apply_id错误');
        }
        //支付渠道信息
        $paymentChannel = $paymentChannelModel->where('id', $paymentAddfund['channel_id'])->first();
        //用户信息
        $users = $userModel->whereIn('id', [$paymentAddfund['audit_user_id'], $paymentAddfund['apply_user_id']])->findAll();
        $users = array_column($users, null, 'id');
        //付款凭证图片
        $paymentImgs = $fileUploadModel->where(['related_id' => $applyId, 'upload_type' => FileUploadModel::UPLOAD_TYPE_RECHARGE])->findAll();

        $data = [
            'id' => $paymentAddfund['id'],//申请记录id
            'customer_id' => $paymentAddfund['customer_id'],
            'customer_name' => $customers[$paymentAddfund['customer_id']]['customer_name'] ?? '',
            'business_type' => $paymentAddfund['business_type'],//业务类型
            'business_type_text' => PaymentAddfundModel::businessType($paymentAddfund['business_type']),
            'payment_type' => $paymentAddfund['payment_type'],//支付方式
            'payment_type_text' => PaymentAddfundModel::paymentType($paymentAddfund['payment_type']),
            'channel_id' => $paymentAddfund['channel_id'],//支付渠道ID
            'channel_name' => $paymentChannel['channel_name'] ?? '',//支付渠道名称
            'order_id' => $paymentAddfund['order_id'],
            'amount' => $paymentAddfund['amount'],//交易金额
            'apply_remark' => $paymentAddfund['apply_remark'],//申请备注
            'audit_remark' => $paymentAddfund['audit_remark'],//审核备注
            'payment_date' => $paymentAddfund['payment_date'],//汇款日期
            'apply_user_id' => $paymentAddfund['apply_user_id'],//申请用户ID
            'apply_user_name' =>  $users[$paymentAddfund['apply_user_id']]['name'] ?? '',//申请用户名称
            'audit_user_id' => $paymentAddfund['audit_user_id'],//审核用户ID
            'audit_user_name' =>  $users[$paymentAddfund['audit_user_id']]['name'] ?? '',//审核用户名称
            'apply_at' => date('Y-m-d H:i:s', $paymentAddfund['apply_at']),//申请时间
            'status' => $paymentAddfund['status'],
            'status_text' => PaymentAddfundModel::status($paymentAddfund['status']),
            'paymentImgs' => $paymentImgs
        ];

        return $data;
    }

    /**
     * @desc 线下充值申请
     * @param $params
     * @return bool|int|string
     * @throws \Exception
     *
     * <AUTHOR> 2025-06-12
     */
    public function rechargeApply($params)
    {
        $paymentAddfundModel = model('PaymentAddfundModel');
        $fileUploadModel = model('FileUploadModel');
        $paymentChannelModel = model('PaymentChannelModel');
        $customerModel = model('CustomerModel');

        $customerId = intval($params['customer_id']);//客户ID
        $amount = floatval($params['amount']);//充值金额
        $remark = trim($params['remark']);//备注
        $paymentType = intval($params['payment_type']);//支付方式
        $channelId = intval($params['channel_id']);//支付渠道ID
        $paymentDate = trim($params['payment_date']);//汇款日期
        $transferAccountNumber = trim($params['transfer_account_number']);//汇款银行账号
        $transferAccountName = trim($params['transfer_account_name']);//汇款银行账号
        $paymentImages = $params['payment_images'];//凭证图片
        $qrcodePaymentChannel = intval($params['qrcode_payment_channel']);//付款渠道
        $qrcodePaymentNickname = trim($params['qrcode_payment_nickname']);//付款账号/付款人名称
        $posBankName = trim($params['pos_bank_name']);//刷卡银行
        $posAccountNumber = trim($params['pos_account_number']);//刷卡卡号

        if (count($paymentImages) > 5) {
            throw new \Exception("凭证图片最多5张");
        }
        $customer = $customerModel->find($customerId);
        if (empty($customer)) {
            throw new \Exception("非法customer_id");
        }
        $paymentChannel = $paymentChannelModel->find($channelId);
        if (empty($paymentChannel)) {
            throw new \Exception("非法channel_id");
        }
        $addPayment = [];
        switch ($paymentType) {
            case PaymentAddfundModel::PAYMENT_TYPE_OFFLINE_TRANSFER:
                if (empty($transferAccountNumber) || empty($transferAccountName)) {
                    throw new \Exception("汇款银行账号、汇款账号名称不允许为空");
                }
                $addPayment = [
                    'transfer_account_number' => $transferAccountNumber,
                    'transfer_account_name' => $transferAccountName,
                ];
                break;
            case PaymentAddfundModel::PAYMENT_TYPE_OFFLINE_QR_CODE_COLLECTION:
                if (empty($qrcodePaymentChannel) || empty($qrcodePaymentNickname)) {
                    throw new \Exception("付款渠道、付款账号/付款人名称不允许为空");
                }
                $addPayment = [
                    'qrcode_payment_channel' => $qrcodePaymentChannel,
                    'qrcode_payment_nickname' => $qrcodePaymentNickname,
                ];
                break;
            case PaymentAddfundModel::PAYMENT_TYPE_OFFLINE_POS_MACHINE_COLLECTION:
                if (empty($posBankName) || empty($posAccountNumber)) {
                    throw new \Exception("刷卡银行、刷卡卡号名称不允许为空");
                }
                $addPayment = [
                    'pos_bank_name' => $posBankName,
                    'pos_account_number' => $posAccountNumber,
                ];
                break;
            default:
                break;
        }
        try {
            $db = \Config\Database::connect();
            $db->transStart();
            $addPayment['customer_id'] = $customerId;
            $addPayment['business_type'] = PaymentAddfundModel::BUSINESS_TYPE_RECHARGE;
            $addPayment['payment_type'] = $paymentType;
            $addPayment['channel_id'] = $channelId;
            $addPayment['amount'] = $amount;
            $addPayment['apply_remark'] = $remark;
            $addPayment['payment_date'] = $paymentDate;
            $addPayment['apply_user_id'] = 1;//TODO 暂写死
            $addPayment['apply_at'] = time();
            $addPayment['status'] = PaymentAddfundModel::STATUS_SUBMITTED;
            $paymentAddfundId = $paymentAddfundModel->insert($addPayment);
            foreach ($paymentImages as $paymentImage) {
                $fileUploadModel->insert([
                    'upload_type' => FileUploadModel::UPLOAD_TYPE_RECHARGE,
                    'related_id' => $paymentAddfundId,
                    'file_type' => FileUploadModel::FILE_TYPE_IMG,
                    'file_path' => FileUploadModel::FILE_PATH_PAYMENT,
                    'file_name' => $paymentImage,
                    'thumbnail' => 'thumb_' . $paymentImage,
                    'status' => FileUploadModel::STATUS_ENABLE,
                ]);
            }
            $db->transComplete();
        } catch (\Exception $e) {
            $db->transRollback();
            throw new \Exception($e->getMessage());
        }

        $data = [
            "customer_id" => $customer['id'],
            'card_no' => $customer['card_no'],
            'amount' => $amount,
            'customer_name' => $customer['customer_name'],
        ];
        return $data;
    }

    //线下充值审核
    public function rechargeAudit($params)
    {
        $paymentAddfundModel = model('PaymentAddfundModel');
        $customerAccountModel = model('CustomerAccountModel');
        $customerCashTransactionModel = model('CustomerCashTransactionModel');

        $paymentAddfundId = $params['payment_addfund_id'];
        $status = $params['status'];//审核状态
        $remark = $params['remark'];//备注

        $paymentAddfund = $paymentAddfundModel->find($paymentAddfundId);
        if (empty($paymentAddfund)) {
            throw new \Exception("payment_addfund_id错误");
        }
        if ($paymentAddfund['status'] != PaymentAddfundModel::STATUS_SUBMITTED) {
            throw new \Exception("请勿重复审核");
        }
        $customerId = $paymentAddfund['customer_id'];
        $customerAccount = $customerAccountModel->where('customer_id', $customerId)->first();
        if (empty($customerAccount)) {
            throw new \Exception("未查询到customerAccount表信息");
        }
        if ($customerAccount['cash_flag'] == CustomerAccountModel::CASH_FLAG_UNOPEN) {
            throw new \Exception("预存金账户未开通");
        }
        try {
            $db = \Config\Database::connect();
            $db->transStart();
            $transactionNo = $customerCashTransactionModel->generateTransactionNo();
            //1.修改充值申请表状态  TODO audit_user_id暂写死
            $paymentAddfundModel->where('id', $paymentAddfundId)->set(['status' => $status, 'audit_remark' => $remark, 'audit_user_id' => 1, 'audit_at' => time()])->update();
            //2.审核通过时，写流水、修改余额
            if ($status == PaymentAddfundModel::STATUS_APPROVED) {
                $cashBalance = bcadd($customerAccount['cash_balance'], $paymentAddfund['amount'], 2);
                $customerAccountModel->where('customer_id', $customerId)->set('cash_balance', $cashBalance)->update();
                $customerCashTransactionModel->insert([
                    'transaction_no' => $transactionNo,
                    'customer_id' => $customerId,
                    'transaction_type' => CustomerCashTransactionModel::TRANSACTION_TYPE_INCOME,
                    'subject_id' => CustomerCashSubjectModel::SUBJECT_TYPE_RECHARGE,
                    'amount' => $paymentAddfund['amount'],
                    'current_balance' => $cashBalance,
                    'user_id' => 1,//TODO user_id暂写死
                    'created_date' => date('Y-m-d', time()),
                ]);
            }
            $db->transComplete();
        } catch (\Exception $e) {
            //TODO 记录错误日志
            $db->transRollback();
            error(0, $e->getMessage());
        }

        return true;
    }

}