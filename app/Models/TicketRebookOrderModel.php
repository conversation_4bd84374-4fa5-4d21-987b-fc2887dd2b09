<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketRebookOrderModel extends Model
{
    protected $table            = 'ticket_rebook_orders';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'int';

    //客户付款状态：0未付款 1付款中 2已付款 3付款失败
    const CUSTOMER_PAYMENT_FLAG_UNPAID = 0;
    const CUSTOMER_PAYMENT_FLAG_PAIDING = 1;
    const CUSTOMER_PAYMENT_FLAG_PAID = 2;
    const CUSTOMER_PAYMENT_FLAG_FAILED = 3;

    //订单状态
    const ORDER_STATUS = [0 => '已申请', 1 => '改签中', 2 => '已改签', 3 => '已取消', 4 => '改签失败'];

    //收付款状态
    const CUSOTMER_PAYMENT_STATUS = [0 => '未收款', 1 => '已收款'];

    //生成订单号 | T出票 R退款 V作废 改签C
    public function generate_order_no($prefix = 'C')
    {
        // 获取当前时间戳（10位）
        $timestamp = time();
        // 生成2位随机数补足12位
        $random = mt_rand(10, 99);
        // 组合成12位数字（时间戳后8位+随机2位+微秒2位）
        $micro  = substr(microtime(), 2, 2);
        $number = substr($timestamp, 2) . $random . $micro;
        // 确保正好12位
        $number = substr($number, 0, 12);

        return $prefix . $number;
    }

    //分页列表
    public function paginate_list($where, $page, $perPage)
    {
        $book_seg_model     = model('TicketBookSegModel');
        $rebook_pax_model = model('TicketRebookPaxModel');
        $rebook_seg_model   = model('TicketRebookSegModel');
        $flightModel                  = model('FlightModel');

        $this->handle_conditions($where);
        $this->orderBy('id', 'DESC');
        $list = $this->paginate($perPage, 'default', $page);
        if (empty($list)) {
            return [];
        }

        //获取原有订单ids
        $order_ids               = array_column($list, 'id');
        $origin_order_ids        = array_column($list, 'origin_order_type', 'origin_order_id');
        $origin_book_order_ids   = [];
        $origin_rebook_order_ids = [];
        foreach ($origin_order_ids as $k => $v) {
            if ($v == 1) {
                $origin_book_order_ids[] = $k;
            } else if ($v == 2) {
                $origin_rebook_order_ids[] = $v;
            }
        }

        //订单乘客
        $order_passenger_list = $rebook_pax_model->whereIn('order_id', $order_ids)->findAll();
        $order_passengers     = [];
        foreach ($order_passenger_list as $val) {
            $order_id                      = $val['order_id'];
            $order_passengers[$order_id][] = [
                'order_id'      => $val['order_id'],
                'person_name'   => $val['person_name'],
                'ticket_number' => $val['ticket_number'],
            ];
        }
        //改签后航段
        $after_order_segment_list = $rebook_seg_model->whereIn('order_id', $order_ids)->findAll();

        //改签前航段
        //出票订单航段
        $before_order_segment_list_book = [];
        if (!empty($origin_book_order_ids)) {
            $before_order_segment_list_book = $book_seg_model->whereIn('order_id', $origin_book_order_ids)->findAll();
        }
        //改签订单航段
        $before_order_segment_list_rebook = [];
        if (!empty($origin_rebook_order_ids)) {
            $before_order_segment_list_rebook = $rebook_seg_model->whereIn('order_id', $origin_rebook_order_ids)->findAll();
        }
        $all_segments   = array_merge($after_order_segment_list, $before_order_segment_list_book, $before_order_segment_list_rebook);
        $flight_numbers = array_column($all_segments, 'flight_number');

        //航班
        $flight_list = $flightModel->whereIn('flight_number', $flight_numbers)->findAll();
        $flight_list = array_column($flight_list, null, 'flight_number');

        //改签后按订单ID找航段信息
        $after_order_segments = [];
        foreach ($after_order_segment_list as $val) {
            $flight_number                     = $val['flight_number'];
            $order_id                          = $val['order_id'];
            $after_order_segments[$order_id][] = [
                'order_id'           => $val['order_id'],
                'flight_number'      => $flight_number,
                'departure_airport'  => $flight_list[$flight_number]['departure_airport'],//出发机场代码
                'arrival_airport'    => $flight_list[$flight_number]['arrival_airport'],//到达机场代码
                'cabin'              => $val['cabin'],//舱位
                'departure_datetime' => explode(' ', $val['departure_datetime'])[0],//出发日期
            ];
        }
        //改签前按订单ID找航段信息(出票订单)
        $before_order_segments_book = [];
        foreach ($before_order_segment_list_book as $val) {
            $flight_number                               = $val['flight_number'];
            $order_id                                    = $val['order_id'];
            $before_order_segment_list_book[$order_id][] = [
                'order_id'           => $val['order_id'],
                'flight_number'      => $flight_number,
                'departure_airport'  => $flight_list[$flight_number]['departure_airport'],//出发机场代码
                'arrival_airport'    => $flight_list[$flight_number]['arrival_airport'],//到达机场代码
                'cabin'              => $val['cabin'],//舱位
                'departure_datetime' => explode(' ', $val['departure_datetime'])[0],//出发日期
            ];
        }
        $before_order_segments_rebook = [];
        foreach ($before_order_segment_list_rebook as $val) {
            $flight_number                                 = $val['flight_number'];
            $order_id                                      = $val['order_id'];
            $before_order_segment_list_rebook[$order_id][] = [
                'order_id'           => $val['order_id'],
                'flight_number'      => $flight_number,
                'departure_airport'  => $flight_list[$flight_number]['departure_airport'],//出发机场代码
                'arrival_airport'    => $flight_list[$flight_number]['arrival_airport'],//到达机场代码
                'cabin'              => $val['cabin'],//舱位
                'departure_datetime' => explode(' ', $val['departure_datetime'])[0],//出发日期
            ];
        }

        $data = [];
        foreach ($list as $value) {
            $str_passenger = '';
            foreach ($order_passengers[$value['id']] as $passenger) {
                if ($str_passenger == '') {
                    $str_passenger = $passenger['person_name'];
                } else {
                    $str_passenger = '<br>' . $passenger['person_name'];
                }
            }
            //对比改签前和改签后航段
            $after_order_segment = $after_order_segments[$value['id']];
            if ($value['origin_order_type'] == 1) {
                $before_order_segment = $before_order_segment_list_book[$value['origin_order_id']];
            } else {
                $before_order_segment = $before_order_segment_list_rebook[$value['origin_order_id']];
            }
            $after_order_segment  = array_column($after_order_segment, null, 'flight_number');
            $before_order_segment = array_column($before_order_segment, null, 'flight_number');
            $new_segments         = $this->array_diff_assoc_recursive($after_order_segment, $before_order_segment);
            $old_segments         = $this->array_diff_assoc_recursive($before_order_segment, $after_order_segment);
            $str_new_segment      = '';
            foreach ($new_segments as $segment) {
                if (!empty($str_new_segment)) {
                    $str_new_segment .= '<br>';
                }
                $str_new_segment = $segment['flight_number'] . ' ' . $segment['departure_airport'] . '-' . $segment['arrival_airport'] . ' / ' . $segment['cabin'] . '<br>' . $segment['departure_datetime'];
            }
            $str_old_segment = '';
            foreach ($old_segments as $segment) {
                if (!empty($str_old_segment)) {
                    $str_old_segment .= '<br>';
                }
                $str_old_segment = $segment['flight_number'] . ' ' . $segment['departure_airport'] . '-' . $segment['arrival_airport'] . ' / ' . $segment['cabin'] . '<br>' . $segment['departure_datetime'];
            }

            $data[] = [
                'order_id'              => $value['id'],
                'order_no'              => $value['order_no'],
                'origin_order_no'       => $value['origin_order_no'],
                'pnr'                   => $value['pnr'],
                'origin_pnr'            => $value['origin_pnr'],
                'airline_pnr'           => $value['airline_pnr'],//大编码
                'total_supplier_amount'           => $value['total_supplier_amount'],//大编码
                'total_customer_amount'           => $value['total_customer_amount'],//大编码
                'operator_name'         => $value['operator_name'],//订票员
                'created_at'            => date('Y-m-d H:i:s', $value['created_at']),//下单时间
                'customer_payment_flag' => $value['customer_payment_flag'],
                'customer_payment_flag_text' => self::CUSOTMER_PAYMENT_STATUS[$value['customer_payment_flag']],
                'status'                => $value['status'],//订单状态
                'status_text'           => self::ORDER_STATUS[$value['status']],
                //'journey_info' => implode("-", str_split($value['journey_info'], 3)), //航程
                'journey_type_text'     => OrderModel::JOURNEY_TYPES[$value['journey_type']] ?? '',
                'total_customer_amount' => $value['total_customer_amount'],//订单总额
                'passengers'            => $str_passenger,//乘客信息
                'new_segment'           => $str_new_segment,//改签航程
                'old_segment'           => $str_old_segment,//原航程
            ];
        }

        return $data;
    }

    private function array_diff_assoc_recursive(array $array1, array $array2): array
    {
        $diff = [];
        foreach ($array1 as $key => $value) {
            if (array_key_exists($key, $array2)) {
                if (is_array($value) && is_array($array2[$key])) {
                    // 转成json字符串进行比较
                    if (json_encode($value) !== json_encode($array2[$key])) {
                        $diff[$key] = $value;
                    }
                    //$recursiveDiff = $this->array_diff_assoc_recursive($value, $array2[$key]);
                    //if (!empty($recursiveDiff)) {
                    //    $diff[$key] = $recursiveDiff;
                    //}
                } elseif (is_object($value) && is_object($array2[$key])) {
                    // 对象比较 - 根据实际需求调整
                    if ($value != $array2[$key]) {
                        $diff[$key] = $value;
                    }
                } else {
                    // 标量值直接比较
                    if ($value !== $array2[$key]) {
                        $diff[$key] = $value;
                    }
                }
            } else {
                $diff[$key] = $value;
            }
        }

        return $diff;
    }

    //按状态分组统计
    public function total_status($where)
    {
        $this->handle_conditions($where);
        $total_list = $this->select('status, COUNT(*) as total')
                           ->groupBy('status')
                           ->orderBy('total', 'DESC')
                           ->findAll();
        $total_list = array_column($total_list, null, 'status');
        $data       = [];
        foreach (self::ORDER_STATUS as $key => $value) {
            $total  = $total_list[$key]['total'] ?? 0;
            $data[] = [
                'status'      => $key,
                'status_text' => $value,
                'total'       => $total,
            ];
        }

        return $data;
    }

    private function handle_conditions($where)
    {
        foreach ($where as $k => $v) {
            switch ($k) {
                case 'pnr':
                    $this->where(" pnr = " . $this->db->escape($v) . " or origin_pnr = " . $this->db->escape($v) . " ");
                    break;
                case 'created_at_from':
                    $this->where('created_at >=', $v);
                    break;
                case 'created_at_to':
                    $this->where('created_at <=', $v);
                    break;
                case 'ids':
                    if ($v) {
                        $this->whereIn('id', $v);
                    }
                    break;
                case 'order_no_like':
                    $this->like('order_no', $v, 'after');
                    break;
                default:
                    $this->where($k, $v);
            }
        }
    }
}