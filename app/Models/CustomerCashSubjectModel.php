<?php

namespace App\Models;

use CodeIgniter\Model;

class CustomerCashSubjectModel extends Model
{
    protected $table = 'customer_cash_subject';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat = 'int';

    //交易类型：101充值 102机票退款 104退款转存 201机票支付
    const SUBJECT_TYPE_RECHARGE = 101;
    const SUBJECT_TYPE_AIR_REFUND = 102;
    const SUBJECT_TYPE_TRANSFER_DEPOSIT = 104;
    const SUBJECT_TYPE_AIR_PAID = 201;


}