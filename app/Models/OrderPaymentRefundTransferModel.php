<?php

namespace App\Models;

use CodeIgniter\Model;

class OrderPaymentRefundTransferModel extends Model
{
    protected $table            = 'order_payment_refund_transfer';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'int';

    //状态：0已提交 1审核通过 2审核不通过
    const STATUS_SUBMITTED = 0;
    const STATUS_APPROVED = 1;
    const STATUS_REVIEW_NOT_PASSED = 2;
}