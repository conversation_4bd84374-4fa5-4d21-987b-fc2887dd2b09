<?php

namespace App\Models;

use CodeIgniter\Model;

class OrderPaymentRefundModel extends Model
{
    protected $table            = 'order_payment_refund';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'int';

    //订单类型：3退票订单 4废票订单
    const ORDER_TYPE_REFUND = 3;
    const ORDER_TYPE_INVALID = 4;

    //付款类型：1原路返回 2转为预存金 3银行转账退款
    const PAYMENT_TYPE_RETURN_CAME = 1;
    const PAYMENT_TYPE_CONVERT_CASH = 2;
    const PAYMENT_TYPE_BANK_TRANSFER_REFUND = 3;

    //原有支付类型：1预存金支付 2授信支付 3线下支付 4在线支付
    const ORIGIN_PAYMENT_TYPE_CASH = 1;
    const ORIGIN_PAYMENT_TYPE_CREDIT = 2;
    const ORIGIN_PAYMENT_TYPE_OFFLINE = 3;
    const ORIGIN_PAYMENT_TYPE_ONLINE = 4;

    //状态：0未付款 1付款中 2已付款 3付款失败
    const STATUS_UNPAID = 0;
    const STATUS_PAIDING = 1;
    const STATUS_PAID = 2;
    const STATUS_PAID_FAILED = 3;
}