<?php

namespace App\Models;

use CodeIgniter\Model;

class CustomerCreditSubjectModel extends Model
{
    protected $table = 'customer_credit_subject';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat = 'int';

    //交易类型：1001机票支付 1002保险支付 1003机票改签 2001机票退款 2002保险退款
    const SUBJECT_TYPE_AIR_PAID = 1001;
    const SUBJECT_TYPE_INSURANCE_PAID = 1002;
    const SUBJECT_TYPE_AIR_CHANGE = 1003;
    const SUBJECT_TYPE_AIR_REFUND = 2001;
    const SUBJECT_TYPE_INSURANCE_REFUND = 2002;


}