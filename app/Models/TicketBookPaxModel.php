<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketBookPaxModel extends Model {
    protected $table = 'ticket_book_pax';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat = 'int'; 

    //旅客类型：1成人 2儿童 3婴儿
    const PASSENGER_TYPE = [1=>'成人', 2=>'儿童', 3=>'婴儿'];

    //状态：1已出票 2已改签 3已退票 4已废票
    const STATUS_ISSUED = 1;
    const STATUS_CHANGED = 2;
    const STATUS_REFUNDED = 3;
    const STATUS_INVALID = 4;
}