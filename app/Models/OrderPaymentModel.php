<?php

namespace App\Models;

use CodeIgniter\Model;

class OrderPaymentModel extends Model
{
    protected $table            = 'order_payment';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'int';

    //订单类型：1出票订单 2改签订单 3退款订单
    const ORDER_TYPE_ISSUE = 1;
    const ORDER_TYPE_CHANGE = 2;
    const ORDER_TYPE_REFUND = 3;

    //支付类型：1预存金支付 2授信支付 3线下支付（线下转账） 4线下支付（线下二维码收款） 5在线支付
    const PAYMENT_TYPE_CASH = 1;
    const PAYMENT_TYPE_CREDIT = 2;
    const PAYMENT_TYPE_OFFLINE_TURN = 3;
    const PAYMENT_TYPE_OFFLINE_QRCODE = 4;
    const PAYMENT_TYPE_ONLINE = 5;

    //支付类型：1预存金支付 2授信支付 3线下支付（线下转账） 4线下支付（线下二维码收款） 5在线支付
    const PAYMENT_TYPE = [1 => '预存金支付', 2 => '授信支付', 3 => '线下转账', 4 => '线下二维码收款', 5 => '5在线支付'];

    //状态：0未支付 1支付中 2已支付 3支付失败
    const STATUS_UNPAID = 0;
    const STATUS_PAIDING = 1;
    const STATUS_PAID = 2;
    const STATUS_PAID_FAILED = 3;
}