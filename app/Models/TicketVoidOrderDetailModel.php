<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketVoidOrderDetailModel extends Model
{
    protected $table = 'ticket_void_order_detail';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat = 'int';

    //产品类型：1机票 2保险 3附加产品
    const PRODUCT_TYPE_AIR = 1;
    const PRODUCT_TYPE_INSURANCE = 2;
    const PRODUCT_TYPE_ADDITIONAL = 3;
}