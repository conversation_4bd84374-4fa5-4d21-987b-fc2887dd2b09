<?php

namespace App\Models;

use CodeIgniter\Model;

class TicketVoidOrderModel extends Model
{
    protected $table            = 'ticket_void_orders';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = false;

    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'int';

    //客户退款状态：0未退款 1退款中 2已退款 3退款失败
    const CUSTOMER_REFUND_FLAG_UNPAID  = 0;
    const CUSTOMER_REFUND_FLAG_PAIDING = 1;
    const CUSTOMER_REFUND_FLAG_PAID    = 2;
    const CUSTOMER_REFUND_FLAG_FAILED  = 3;

    //退票订单状态：0已申请 1废票中 2已废票 3已取消 4废票失败
    const ORDER_STATUS_APPLIED     = 0;
    const ORDER_STATUS_VOIDING     = 1;
    const ORDER_STATUS_VOIDED      = 2;
    const ORDER_STATUS_CANCELLED   = 3;
    const ORDER_STATUS_VOID_FAILED = 4;

    //从出票订单或改签订单退改：1出票订单 2改签订单
    const ORIGIN_ORDER_TYPE_ISSUE  = 1;
    const ORIGIN_ORDER_TYPE_CHANGE = 2;

}