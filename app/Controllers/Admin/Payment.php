<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Helpers\Tools;
use App\Models\PnrPassengerModel;
use App\Models\OrderModel;
use App\Models\OrderPassengerModel;

class Payment extends AdminController
{
    /**
     * @desc 设置订单会员ID
     *
     * <AUTHOR> 2025-06-24
     */
    public function setCustomerId()
    {
        exit('改成在提交支付时，同时设置会员id以及会员类型');
        $validation = service('validation');
        $rules      = [
            'order_type' => ['label' => '订单类型', 'rules' => 'required|in_list[1,2,3]'],//订单类型：1出票订单 2改签订单 3退款订单
            'order_id'   => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
            'customer_id'   => ['label' => '会员id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Order\Payment $service */
        $service = load_service('Order\Payment');

        try {
            $res = $service->setCustomerId($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('设置成功', $res);
    }

    /**
     * @desc 订单支付
     *
     * <AUTHOR> 2025-06-24
     */
    public function pay()
    {
        $validation = service('validation');
        $rules = [
            'order_type'              => ['label' => '订单类型', 'rules' => 'required|in_list[1,2]'],//订单类型：1出票订单 2改签订单
            'order_id'                => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
            'customer_id'             => ['label' => 'customer_id', 'rules' => 'required|greater_than[0]'],
            'balance'                 => ['label' => '余额', 'rules' => 'required|greater_than_equal_to[0]'],
            'amount'                  => ['label' => '付款金额', 'rules' => 'required|greater_than[0]'],
            'payment_type'            => ['label' => '支付类型', 'rules' => 'required|in_list[1,2,3]'],//支付类型：1预存金支付 2授信支付 3线下支付 4在线支付
            'channel_id'              => ['label' => '支付渠道ID', 'rules' => 'permit_empty|greater_than[0]'],
            'payment_date'            => ['label' => '汇款日期', 'rules' => 'permit_empty|max_length[10]|min_length[10]'],
            'transfer_account_number' => ['label' => '汇款银行账号', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //汇款账号
            'transfer_account_name'   => ['label' => '汇款银行账号', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //汇款户名
            'payment_images'          => ['label' => '凭证图片', 'rules' => 'permit_empty|is_array'],
            'qrcode_payment_channel'  => ['label' => '支付方式', 'rules' => 'permit_empty|in_list[1,2,3,4]'],//付款渠道：1支付宝 2微信 3云闪付 4其他
            'qrcode_payment_nickname' => ['label' => '付款账号/付款人名称', 'rules' => 'permit_empty|max_length[50]|min_length[2]'], //付款人名称
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        try {
            //设置订单会员ID
            /** @var \App\Services\Order\Payment $service */
            $service = load_service('Order\Payment');
            $service->setCustomerId($validData);
            $res = $service->pay($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('操作成功', $res);
    }

    /**
     * @desc 退款（退票/废票）
     *
     * <AUTHOR> 2025-06-30
     */
    public function refund()
    {
        $validation = service('validation');
        $rules = [
            'order_type'     => ['label' => '订单类型', 'rules' => 'required|in_list[3,4]'],//订单类型：3退票订单 4废票订单
            'order_id'       => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
            'payment_type'   => ['label' => '付款类型', 'rules' => 'required|in_list[1,2,3]'],//付款类型：1原路返回 2转为预存金 3银行转账退款
            'account_name'   => ['label' => '收款人姓名', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'account_number' => ['label' => '收款银行卡号', 'rules' => 'permit_empty|max_length[50]|min_length[2]'],
            'bank_id'        => ['label' => '所属银行', 'rules' => 'permit_empty|greater_than[0]'],
            'amount'         => ['label' => '退款金额', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\Order\Payment $service */
        $service = load_service('Order\Payment');
        try {
            $res = $service->refund($validData);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }

        success('操作成功', $res);
    }

}