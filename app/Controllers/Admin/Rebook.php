<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Helpers\Tools;
use App\Models\PnrPassengerModel;
use App\Models\OrderModel;
use App\Models\OrderPassengerModel;
use App\Services\PnrData\PullPnrDataService;
use App\Models\PnrTicketSegmentModel;
use App\Models\CabinModel;

class Rebook extends AdminController
{
    //国内改签订单列表
    public function orderList()
    {
        $validation = service('validation');
        $rules      = [
            'page'            => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page'        => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'pnr'             => ['label' => '新/原PNR编码', 'rules' => 'permit_empty|max_length[6]|min_length[3]'],
            'order_no'        => ['label' => '改签单号', 'rules' => 'permit_empty|max_length[20]|min_length[3]'],
            'person_name'     => ['label' => '乘客姓名', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_status'    => ['label' => '订单状态', 'rules' => 'permit_empty|greater_than_equal_to[0]'],//小于0则失败
            'origin_order_no' => ['label' => '原订单编号', 'rules' => 'permit_empty|max_length[20]|min_length[13]'],
            'airline'         => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'operator_name'   => ['label' => '订票员', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_date'      => ['label' => '改签日期', 'rules' => 'permit_empty|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJson(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $rebookService = load_service('RebookService');
        $data          = $rebookService->orderList($validData);

        success('成功', $data);
    }

    //国内改签申请
    public function apply()
    {
        $validation = service('validation');
        $rules = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);

        $order_model = model('TicketBookOrderModel');
        $book_order_passenger_model = model('TicketBookPaxModel');
        $user_model = model('UserModel');
        $department_model = model('DepartmentModel');
        $pnr_ticket_model = model('PnrTicketModel');
        $order_passenger_segment_model = model('TicketBookPaxSegModel');
        $airportModel = model('AirportModel');
        $airlineModel = model('AirlineModel');
        $cabinModel = model('CabinModel');
        $order_segment_model = model('TicketBookSegModel');
        $flightModel = model('FlightModel');

        //订单信息
        $order = $order_model->find($order_id);
        if (empty($order)) {
            error(0, 'order_id错误');
        }
        if (!in_array($order['status'], [2,3])) {
            error(0, '只有【已出票】【部分出票】状态才可申请改签');
        }
        $user = $user_model->find($order['operator_id']);
        $department = $department_model->find($user['department_id']);
        $order_data = [
            'order_no' => $order['order_no'],//订单编号
            'pnr' => $order['pnr'],
            'customer_type' => OrderModel::CUSTOMER_TYPES[$order['customer_type']],//订单类型
            'created_at' => date('Y-m-d H:i:s', $order['created_at']),//下单时间
            'operator_name' => $department['department_name'] . '|' . $user['name'],//操作员
            'journey_type' => $order['journey_type'],//航程类型
            'journey_type_text' => OrderModel::JOURNEY_TYPES[$order['journey_type']],//航程类型名称
        ];

        //乘客信息
        $passenger_data = [];
        $order_passengers = $book_order_passenger_model->where('order_id', $order_id)->findAll();
        $order_passenger_ids = array_column($order_passengers, 'id');

        $order_passenger_segment_arr = [];//按ticket_id分组
        $allow_flight_numbers = [];//允许退票的航班
        $order_passenger_segments = $order_passenger_segment_model->whereIn('passenger_id', $order_passenger_ids)->findAll();
        foreach ($order_passenger_segments as $ps) {
            $ticket_id = $ps['passenger_id'];
            $order_passenger_segment_arr[$ticket_id][] = [
                'ticket_id' => $ticket_id,
                'flight_number' => $ps['flight_number'],
                'status' => $ps['status'],//只有在1时可用
                'status_text' => PnrTicketSegmentModel::STATUS[$ps['status']],
            ];

            if ($ps['status'] == 1) {
                $allow_flight_numbers[$ticket_id][] = $ps['flight_number'];
            }
        }
        foreach ($order_passengers as $pp) {
            $ticket_number = $pp['ticket_number'];
            $ticket_id = $pp['id'];
            /**
            【退改状态】 计算规则：
            1.只要有一张状态为 【1.可使用(OPEN FOR USE)】 则退改状态为：可改签
            2.一张 可使用 都没有，则为 不可改
            3.全部都是 【4.已改签(EXCHANGED)】    则为 已改签
             **/
            //电子票号对应航段数组
            $ticket_segments = $order_passenger_segment_arr[$ticket_id];
            $available_qty = 0;//可改签数量
            $changed_qyt = 0;//已改签数
            foreach ($ticket_segments as $ts) {
                if ($ts['status'] == 1) {
                    $available_qty += 1;
                } elseif ($ts['status'] == 4) {
                    $changed_qyt += 1;
                }
            }
            if ($available_qty >= 1) {
                $changed_status = '可改签';
            } elseif ($changed_qyt == count($ticket_segments)) {
                $changed_status = '已改签';
            } else {
                $changed_status = '不可改';
            }
            $passenger_data[] = [
                'rph' => $pp['rph'],//序号
                'ticket_id' => $ticket_id,
                'pnr_passenger_id' => $pp['id'],
                'order_id' => $order_id,
                'person_name' => $pp['person_name'],//乘客姓名
                'gender' => PnrPassengerModel::GENDER[$pp['gender']],//性别
                'passenger_type' => $pp['passenger_type'],//乘客类型
                'passenger_type_text' => PnrPassengerModel::PASSENGER_TYPE[$pp['passenger_type']],//乘客类型文案
                'doc_type' => $pp['doc_type'],//证件类型
                'doc_type_text' => PnrPassengerModel::DOC_TYPE[$pp['doc_type']],//证件类型文案
                'doc_id' => Tools\Idcard::mask($pp['doc_id']),//证件号码
                'telephone' => Tools\Char::mask_phone($pp['telephone']),//联系电话
                'ticket_number' => Tools\Char::mask_ticket($ticket_number),//票号
                'ticket_segments' => $ticket_segments,//电子票号对应航段数组
                'allow_flight_numbers' => $allow_flight_numbers[$ticket_id] ?? [],
                'refund_change_status' => $changed_status//改签状态
            ];
        }
        //航班信息
        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');
        $order_segments = $order_segment_model->where('order_id', $order_id)->findAll();
        $flights = $flightModel->whereIn('flight_number', array_column($order_segments, 'flight_number'))->findAll();
        $flights = array_column($flights, null, 'flight_number');
        $flight_data = [];
        foreach ($order_segments as $ps) {
            $flight = $flights[$ps['flight_number']];
            $airline_code = $flight['airline_code'];//航司编号2位字母
            //航司logo
            $airline_logo = '/static/images/airline/' . $flight['airline_code'] . '.png';
            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airline_code, 'cabin' => $ps['cabin']])->first();
            //航程类型标识
            //航程类型：1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            switch ($order['journey_type']) {
                case 1://单程
                    $journey_name = '单程';
                    break;
                case 2:
                    if ($ps['rph'] == 1) {
                        $journey_name = '去程';
                    } else {
                        $journey_name = '返程';
                    }
                    break;
                case 3:
                case 4:
                case 5:
                    $journey_name = '第' . Tools\Char::to_chinase_num($ps['rph']) . '程';
                    break;
            }
            $flight_data[] = [
                'pnr_segment_id' => $ps['id'],//PNR-航段id
                'journey_name' => $journey_name,
                'airline_logo' => $airline_logo,
                'flight_number' => $ps['flight_number'],
                'airline_code' => $airline_code,//航司编号2位字母
                'airline_cn' => $airlines[$airline_code]['airline_cn'],//航空公司名称
                'air_equip_type' => $flight['air_equip_type'], //机型
                'meal' => '有餐食',//TODO:需要计算出来，此处暂写死
                'departure_airport' => $flight['departure_airport'],//出发机场代码
                'arrival_airport' => $flight['arrival_airport'],//到达机场代码
                'departure_airport_city' => $airports[$flight['departure_airport']]['city_cn'],//出发城市
                'arrival_airport_city' => $airports[$flight['arrival_airport']]['city_cn'],//到达城市
                'departure_airport_cn' => $airports[$flight['departure_airport']]['airport_name_cn'],//出发机场名称
                'arrival_airport_cn' => $airports[$flight['arrival_airport']]['airport_name_cn'],//到达机场名称
                'departure_datetime' => $ps['departure_datetime'],//出发时间
                'arrival_datetime' => $ps['arrival_datetime'],//到达时间
                'cabin' => $ps['cabin'], //舱位编号
                'discount' => $cabin['discount'] . '折',//折扣
                'luggage_weight' => $cabin['luggage_weight'],//行李重量
                'cabin_grade' => CabinModel::CABIN_GRADE[$cabin['cabin_grade_id']],//舱位等级:1经济舱 2公务舱 3头等舱
                //'refund_rate' => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin' => $ps['sub_cabin'],//产品编号
                'customer_explain' => '退票规定:起飞前7天《含)之前:Q舱的20%，起飞前7天(不含)之内至起飞前2天(含)之前:Q舱的30%，起飞前2天(不含)之内至起飞前4小时……'//客规说明
            ];
        }
        $data = [
            'order_data' => $order_data,
            'flight_data' => $flight_data,
            'passenger_data' => $passenger_data,
        ];
        success('获取成功', $data);
    }

    //国内确认改签
    public function confirmChange()
    {
        $validation = service('validation');
        $rules = [
            'tickets' => ['label' => '票号信息', 'rules' => 'required|is_array'],
            'tickets.*.ticket_id' => [
                'label' => '票号id',
                'rules' => 'required|greater_than[0]',
            ],
            'tickets.*.differ_fare' => [
                'label' => '差价',
                'rules' => 'required|greater_than[0]',
            ],
            'tickets.*.changed_fee' => [
                'label' => '改签费',
                'rules' => 'required|greater_than[0]',
            ],
            'tickets.*.marketing_price' => [
                'label' => '票面价',
                'rules' => 'required|greater_than[0]',
            ],
            'old_flight_segments' => ['label' => '旧航段信息', 'rules' => 'required|is_array'],
            'old_flight_segments.*.flight_number' => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]',
            ],
            'old_flight_segments.*.pnr_segment_id' => [
                'label' => 'PNR-航段ID',
                'rules' => 'required|greater_than[0]',
            ],
            'new_flight_segments' => ['label' => '新航段信息', 'rules' => 'required|is_array'],
            'new_flight_segments.*.departure_date' => [
                'label' => '新出发日期',
                'rules' => 'required|valid_date[Y-m-d]',
            ],
            'new_flight_segments.*.flight_number' => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]',
            ],
            'new_flight_segments.*.cabin_no' => [
                'label' => '舱位编号',
                'rules' => 'required|min_length[1]|max_length[30]',
            ],
            'order_id' => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'order_type' => ['label' => '订单类型', 'rules' => 'required|in_list[1,2]'],//订单类型：1出票订单 2改签订单
            'rebook_type' => ['label' => '改签原因', 'rules' => 'required|in_list[1,2]'],//改签类型：1自愿改签 2非自愿改签
            'rebook_purpose' => ['label' => '改签目的', 'rules' => 'required|in_list[1,2]'],//改签目的：1改期 2升舱
            'contact_name' => ['label' => '联系人', 'rules' => 'required|max_length[6]|min_length[2]'],
            'contact_telephone' => ['label' => '联系电话', 'rules' => 'required|max_length[20]|min_length[11]'],
            'contact_email' => ['label' => '联系邮箱', 'rules' => 'required|max_length[30]|min_length[6]'],
            'is_send_sms' => ['label' => '通知类型', 'rules' => 'required|in_list[0,1]'],//发送短信：0否 1是
            'remark' => ['label' => '备注', 'rules' => 'required|max_length[50]|min_length[2]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        /** @var \App\Services\RebookService $rebookService */
        $rebookService  = load_service('RebookService');
        $rebook_order_id = $rebookService->changTicket($validData);

        success('改签成功', ['rebook_order_id' => $rebook_order_id]);
    }

    //国内改签订单详情
    public function orderDetail()
    {
        $validation = service('validation');
        $rules = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);

        $rebook_order_model = model('TicketRebookOrderModel');
        $user_model = model('UserModel');
        $department_model = model('DepartmentModel');
        $airportModel = model('AirportModel');
        $airlineModel = model('AirlineModel');
        $cabinModel = model('CabinModel');
        $book_seg_model = model('TicketBookSegModel');
        $rebook_seg_model = model('TicketRebookSegModel');
        $flightModel = model('FlightModel');
        $rebook_pax_model = model('TicketRebookPaxModel');
        $regook_order_passenger_segment_model = model('TicketRebookPaxSegModel');
        $book_pax_model = model('TicketBookPaxModel');
        $rebook_pax_model = model('TicketRebookPaxModel');
        $book_order_detail_model = model('TicketBookOrderDetailModel');
        $rebook_order_detail_model = model('TicketRebookOrderDetailModel');
        $customerModel         = model('CustomerModel');
        $orderPaymentModel     = model('OrderPaymentModel');
        
        //订单信息
        $order = $rebook_order_model->find($order_id);
        if (empty($order)) {
            error(0, 'order_id错误');
        }
        $origin_order_id = $order['origin_order_id'];//原订单id
        $origin_order_no = $order['origin_order_no'];//原订单编号
        $origin_order_type = $order['origin_order_type'];//原订单类型
        $user = $user_model->find($order['operator_id']);
        $department = $department_model->find($user['department_id']);

        // 客户信息
        $customer_name = '';
        $card_no = '';
        if (!empty($order['customer_id'])) {
            $customer_info = $customerModel->find($order['customer_id']);
            if (!empty($customer_info)) {
                $customer_name = $customer_info['customer_name'];
                $card_no = $customer_info['card_no'];
            }
        }

        // 收款信息
        $payment_type_text = '';
        $payment_time = '';
        $payment_info = $orderPaymentModel->where('order_type', 2)->where('order_id', $order_id)->orderBy('id', 'desc')->first();
        if (!empty($payment_info)) {
            $payment_type_text = OrderPaymentModel::PAYMENT_TYPE[$payment_info['payment_type']];
            $payment_time = date('Y-m-d H:i:s', $payment_info['created_at']);
        }

        $order_data = [
            'order_no' => $order['order_no'],//订单编号
            'relate_order_no' => $origin_order_no,
            'pnr' => $order['pnr'],
            'customer_type' => OrderModel::CUSTOMER_TYPES[$order['customer_type']],//订单类型
            'customer_id'                => $order['customer_id'],
            'customer_name'              => $customer_name,
            'card_no'                    => $card_no,
            'payment_type_text'          => $payment_type_text,
            'payment_time'               => $payment_time,
            'status' => $order['status'],//订单状态
            'status_text' => OrderModel::REBOOK_STATUS[$order['status']],//订单状态名称
            'customer_payment_flag' => $order['customer_payment_flag'],//付款状态
            'customer_payment_flags_text' => OrderModel::CUSTOMER_PAYMENT_FLAG[$order['customer_payment_flag']],//付款状态名称
            'created_at' => date('Y-m-d H:i:s', $order['created_at']),//下单时间
            'changed_at' => date('Y-m-d H:i:s', $order['changed_at']),//改签时间
            'operator_name' => $department['department_name'] . '|' . $user['name'],//操作员
            'rebook_type' => $order['rebook_type'],//改签原因
            'rebook_type_text' => OrderModel::REBOOK_TYPE[$order['rebook_type']],//改签原因名称
            'rebook_purpose' => $order['rebook_purpose'],//改签类型
            'rebook_purpose_text' => OrderModel::REBOOK_PURPOSE[$order['rebook_purpose']],//改签类型名称
            'contact_name' => $order['contact_name'],//联系人
            'contact_telephone' => $order['contact_telephone'],//联系电话
            'contact_email' => $order['contact_email'],//联系邮箱
            'is_send_sms' => $order['is_send_sms'],
            'remark' => $order['remark'],
        ];

        //乘客信息
        $passenger_data = [];
        $order_passengers = $rebook_pax_model->where('order_id', $order_id)->findAll();
        $passenger_ids = array_column($order_passengers, 'id');

        $order_passenger_segment_arr = [];//按ticket_id分组
        $allow_flight_numbers = [];//允许改签的航班
        $order_passenger_segments = $regook_order_passenger_segment_model->whereIn('passenger_id', $passenger_ids)->findAll();
        foreach ($order_passenger_segments as $ps) {
            $ticket_id = $ps['passenger_id'];
            $order_passenger_segment_arr[$ticket_id][] = [
                'ticket_id' => $ticket_id,
                'flight_number' => $ps['flight_number'],
                'status' => $ps['status'],//只有在1时可用
                'status_text' => PnrTicketSegmentModel::STATUS[$ps['status']],
            ];

            if ($ps['status'] == 1) {
                $allow_flight_numbers[$ticket_id][] = $ps['flight_number'];
            }
        }
        foreach ($order_passengers as $pp) {
            $ticket_number = $pp['ticket_number'];
            $ticket_id = $pp['id'];
            /**
            【退改状态】 计算规则：
            1.只要有一张状态为 【1.可使用(OPEN FOR USE)】 则退改状态为：可退票
            2.一张 可使用 都没有，则为 不可退
            3.全部都是 【5.已退票(REFUNDED)】    则为 已退票
             **/
            //电子票号对应航段数组
            $passenger_segments = $order_passenger_segment_arr[$ticket_id];
            $available_qty = 0;//可退票数量
            $changed_qyt = 0;//已退票数
            foreach ($passenger_segments as $ts) {
                if ($ts['status'] == 1) {
                    $available_qty += 1;
                } elseif ($ts['status'] == 4) {
                    $changed_qyt += 1;
                }
            }
            if ($available_qty >= 1) {
                $refund_change_status = '可改签';
            } elseif ($changed_qyt == count($passenger_segments)) {
                $refund_change_status = '已改签';
            } else {
                $refund_change_status = '不可改签';
            }
            $passenger_data[] = [
                'rph' => $pp['rph'],//序号
                'ticket_id' => $ticket_id,
                'pnr_passenger_id' => $pp['id'],
                'order_id' => $order_id,
                'person_name' => $pp['person_name'],//乘客姓名
                'gender' => PnrPassengerModel::GENDER[$pp['gender']],//性别
                'passenger_type' => $pp['passenger_type'],//乘客类型
                'passenger_type_text' => PnrPassengerModel::PASSENGER_TYPE[$pp['passenger_type']],//乘客类型文案
                'doc_type' => $pp['doc_type'],//证件类型
                'doc_type_text' => PnrPassengerModel::DOC_TYPE[$pp['doc_type']],//证件类型文案
                'doc_id' => Tools\Idcard::mask($pp['doc_id']),//证件号码
                'telephone' => Tools\Char::mask_phone($pp['telephone']),//联系电话
                'ticket_number' => Tools\Char::mask_ticket($ticket_number),//票号
                'ticket_segments' => $order_passenger_segment_arr,//电子票号对应航段数组
                'allow_flight_numbers' => $allow_flight_numbers[$ticket_id] ?? [],
                'refund_change_status' => $refund_change_status//退改状态
            ];
        }
        //航班信息
        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');

        if ($order['origin_order_type'] == 1) {
            $origin_segments = $book_seg_model->where('order_id', $order['origin_order_id'])->findAll();
        } else {
            $origin_segments = $rebook_seg_model->where('order_id', $order['origin_order_id'])->findAll();
        }
        
        $flights = $flightModel->whereIn('flight_number', array_column($origin_segments, 'flight_number'))->findAll();
        $flights = array_column($flights, null, 'flight_number');

        $flight_data = [];
        $journey_name = '';
        foreach ($origin_segments as $ps) {
            $flight = $flights[$ps['flight_number']];
            $airline_code = $flight['airline_code'];//航司编号2位字母
            //航司logo
            $airline_logo = '/static/images/airline/' . $flight['airline_code'] . '.png';
            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airline_code, 'cabin' => $ps['cabin']])->first();
            //航程类型标识
            //航程类型：1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            switch ($order['journey_type']) {
                case 1://单程
                    $journey_name = '单程';
                    break;
                case 2:
                    if ($ps['rph'] == 1) {
                        $journey_name = '去程';
                    } else {
                        $journey_name = '返程';
                    }
                    break;
                case 3:
                case 4:
                case 5:
                    $journey_name = '第' . Tools\Char::to_chinase_num($ps['rph']) . '程';
                    break;
            }
            $origin_flight_data[] = [
                'journey_name' => $journey_name,
                'airline_logo' => $airline_logo,
                'flight_number' => $ps['flight_number'],
                'airline_code' => $airline_code,//航司编号2位字母
                'airline_cn' => $airlines[$airline_code]['airline_cn'],//航空公司名称
                'air_equip_type' => $flight['air_equip_type'], //机型
                'meal' => '有餐食',//TODO:需要计算出来，此处暂写死
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                'arrival_airport_cn' => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                'departure_datetime' => $ps['departure_datetime'],//出发时间
                'arrival_datetime' => $ps['arrival_datetime'],//到达时间
                'cabin' => $ps['cabin'], //舱位编号
                'discount' => $cabin['discount'] . '折',//折扣
                'luggage_weight' => $cabin['luggage_weight'],//行李重量
                //'refund_rate' => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin' => $ps['sub_cabin'],//产品编号
                'customer_explain' => '退票规定:起飞前7天《含)之前:Q舱的20%，起飞前7天(不含)之内至起飞前2天(含)之前:Q舱的30%，起飞前2天(不含)之内至起飞前4小时……'//客规说明
            ];
        }

        $new_segments = $rebook_seg_model->where('order_id', $order_id)->findAll();
        
        $flights = $flightModel->whereIn('flight_number', array_column($new_segments, 'flight_number'))->findAll();
        $flights = array_column($flights, null, 'flight_number');

        $flight_data = [];
        $journey_name = '';
        foreach ($new_segments as $ps) {
            $flight = $flights[$ps['flight_number']];
            $airline_code = $flight['airline_code'];//航司编号2位字母
            //航司logo
            $airline_logo = '/static/images/airline/' . $flight['airline_code'] . '.png';
            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airline_code, 'cabin' => $ps['cabin']])->first();
            //航程类型标识
            //航程类型：1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            switch ($order['journey_type']) {
                case 1://单程
                    $journey_name = '单程';
                    break;
                case 2:
                    if ($ps['rph'] == 1) {
                        $journey_name = '去程';
                    } else {
                        $journey_name = '返程';
                    }
                    break;
                case 3:
                case 4:
                case 5:
                    $journey_name = '第' . Tools\Char::to_chinase_num($ps['rph']) . '程';
                    break;
            }
            $new_flight_data[] = [
                'journey_name' => $journey_name,
                'airline_logo' => $airline_logo,
                'flight_number' => $ps['flight_number'],
                'airline_code' => $airline_code,//航司编号2位字母
                'airline_cn' => $airlines[$airline_code]['airline_cn'],//航空公司名称
                'air_equip_type' => $flight['air_equip_type'], //机型
                'meal' => '有餐食',//TODO:需要计算出来，此处暂写死
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                'arrival_airport_cn' => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                'departure_datetime' => $ps['departure_datetime'],//出发时间
                'arrival_datetime' => $ps['arrival_datetime'],//到达时间
                'cabin' => $ps['cabin'], //舱位编号
                'discount' => $cabin['discount'] . '折',//折扣
                'luggage_weight' => $cabin['luggage_weight'],//行李重量
                //'refund_rate' => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin' => $ps['sub_cabin'],//产品编号
                'customer_explain' => '退票规定:起飞前7天《含)之前:Q舱的20%，起飞前7天(不含)之内至起飞前2天(含)之前:Q舱的30%，起飞前2天(不含)之内至起飞前4小时……'//客规说明
            ];
        }

        //价格信息
        $order_detail = $rebook_order_detail_model->where('order_id', $order_id)->findAll();
        $order_passengers = array_column($order_passengers, null, 'id');
        $price_data = [
            'purchase' => [//采购
                'detail' => [],
                'total' => [
                    'total_supplier_amount' => 0,
                ]
            ],
            'sales' => [//销售
                'detail' => [],
                'total' => [
                    'total_customer_amount' => 0,
                ]
            ]
        ];
        foreach ($order_detail as $key => $val) {
            $order_passenger = $order_passengers[$val['order_passenger_id']];
            if ($order_passenger['origin_order_type'] == 1) {
                $origin_passenger = $book_pax_model->find($order_passenger['origin_passenger_id']);
                $origin_order_detail = $book_order_detail_model->where('order_passenger_id', $origin_passenger['id'])->where('product_type', 1)->first();
            } else {
                $origin_passenger = $rebook_pax_model->find($order_passenger['origin_passenger_id']);
                $origin_order_detail = $rebook_order_detail_model->where('order_passenger_id', $origin_passenger['id'])->where('product_type', 1)->first();
            }
            //采购
            $price_data['purchase']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'passenger_id' => $order_passenger['id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'ticket_marketing_price' => $origin_order_detail['ticket_marketing_price'],
                'ticket_tax_cn' => $origin_order_detail['ticket_tax_cn'],
                'ticket_tax_yq' => $origin_order_detail['ticket_tax_yq'],
                'ticket_supplier_price_diff' => $val['ticket_supplier_price_diff'],//票面总价
                'ticket_supplier_tax_diff' => $val['ticket_supplier_tax_diff'],//采购退票费率
                'ticket_supplier_change_fee' => $val['ticket_supplier_change_fee'],//退票手续费
                'ticket_supplier_service_fee' => $val['ticket_supplier_service_fee'],//退票费
                'ticket_supplier_agency_fee' => $val['ticket_supplier_agency_fee'],//机票应退总额
                'supplier_amount' => $val['supplier_amount'],//机票应退总额
            ];

            $price_data['purchase']['total']['total_supplier_amount'] = bcadd($price_data['purchase']['total']['total_supplier_amount'], $val['supplier_amount'], 2);//总票面总价
            //销售
            $price_data['sales']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'passenger_id' => $order_passenger['id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'ticket_marketing_price' => $origin_order_detail['ticket_marketing_price'],
                'ticket_tax_cn' => $origin_order_detail['ticket_tax_cn'],
                'ticket_tax_yq' => $origin_order_detail['ticket_tax_yq'],
                'ticket_customer_price_diff' => $val['ticket_customer_price_diff'],//票面总价
                'ticket_customer_tax_diff' => $val['ticket_customer_tax_diff'],//采购退票费率
                'ticket_customer_change_fee' => $val['ticket_customer_change_fee'],//退票手续费
                'ticket_customer_service_fee' => $val['ticket_customer_service_fee'],//退票费
                'customer_amount' => $val['customer_amount'],//机票应退总额
            ];
            $price_data['sales']['total']['total_customer_amount'] = bcadd($price_data['sales']['total']['total_customer_amount'], $val['customer_amount'], 2);//总票面总价
        }
        $data = [
            'order_data' => $order_data,
            'origin_flight_data' => $origin_flight_data,
            'new_flight_data' => $new_flight_data,
            'passenger_data' => $passenger_data,
            'price_data' => $price_data
        ];

        success('获取成功', $data);
    }

    //保存国内改签订单价格
    public function savePrice()
    {
        $validation = service('validation');
        $rules      = [
            'order_id'                                 => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
            'purchase'                                 => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'purchase.detail'                               => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'purchase.detail.*.passenger_id'                => ['label' => '乘客ID', 'rules' => 'required|greater_than[0]'],
            'purchase.detail.*.ticket_supplier_price_diff'  => ['label' => '采购价差', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_tax_diff'    => ['label' => '采购税差', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_change_fee'  => ['label' => '采购改签费', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_service_fee' => ['label' => '采购服务费', 'rules' => 'required|decimal'],
            'sales'                                      => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'sales.detail'                               => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'sales.detail.*.passenger_id'                => ['label' => '乘客ID', 'rules' => 'required|greater_than[0]'],
            'sales.detail.*.ticket_customer_price_diff'  => ['label' => '销售价差', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_tax_diff'    => ['label' => '销售税差', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_change_fee'  => ['label' => '销售改签费', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_service_fee' => ['label' => '销售服务费', 'rules' => 'required|decimal'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        if (count($validData['purchase']['detail']) != count($validData['sales']['detail'])) {
            error(0, '数据错误');
        }
        $purchase_price_list = $validData['purchase']['detail'];
        $sales_price_list = $validData['sales']['detail'];
        $purchase_price_list = array_column($purchase_price_list, null, 'passenger_id');
        $sales_price_list = array_column($sales_price_list, null, 'passenger_id');
        $passengers = [];
        foreach ($purchase_price_list as $k => $v) {
            if (!isset($sales_price_list[$k])) {
                error(0, '数据错误');
            }
            $passengers[] = [
                'passenger_id' => $k,
                'ticket_supplier_price_diff' => $v['ticket_supplier_price_diff'],
                'ticket_supplier_tax_diff' => $v['ticket_supplier_tax_diff'],
                'ticket_supplier_change_fee' => $v['ticket_supplier_change_fee'],
                'ticket_supplier_service_fee' => $v['ticket_supplier_service_fee'],
                'ticket_supplier_agency_fee' => $v['ticket_supplier_agency_fee'],
                'ticket_customer_price_diff' => $sales_price_list[$k]['ticket_customer_price_diff'],
                'ticket_customer_tax_diff' => $sales_price_list[$k]['ticket_customer_tax_diff'],
                'ticket_customer_change_fee' => $sales_price_list[$k]['ticket_customer_change_fee'],
                'ticket_customer_service_fee' => $sales_price_list[$k]['ticket_customer_service_fee'],
            ];
        }
        $validData['passengers'] = $passengers;

        $rebookService = load_service('RebookService');
        $data    = $rebookService->savePrice($validData);

        success('成功', $data);
    }
}