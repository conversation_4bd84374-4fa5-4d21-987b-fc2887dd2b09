<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Helpers\Tools;
use App\Models\PnrPassengerModel;
use App\Models\OrderModel;
use App\Models\OrderPassengerModel;
use App\Models\OrderPaymentModel;
use App\Services\PnrData\PullPnrDataService;

class Order extends AdminController
{
    //创建订单
    public function createOrder()
    {
        $validation = service('validation');
        $rules      = [
            'flight_segments'                  => ['label' => '航段信息', 'rules' => 'required|is_array'],
            // 'flight_segments.*.rph' => [
            //     'label' => '航段编号',
            //     'rules' => 'required|greater_than[0]'
            // ],
            'flight_segments.*.flight_number'  => [
                'label' => '航班编号',
                'rules' => 'required|min_length[6]|max_length[30]',
            ],
            'flight_segments.*.cabin'          => [
                'label' => '舱位',
                'rules' => 'required|min_length[1]|max_length[30]',
            ],
            'flight_segments.*.departure_date' => [
                'label' => '出发日期',
                'rules' => 'required|valid_date[Y-m-d]',
            ],
            'flight_segments.*.adult_price'    => [
                'label' => '成人价格',
                'rules' => 'required|greater_than[0]',//成人价
            ],
            'flight_segments.*.children_price' => [
                'label' => '儿童价格',
                'rules' => 'required|greater_than[0]',//成人价
            ],
            'flight_segments.*.baby_price'     => [
                'label' => '婴儿价',
                'rules' => 'required|greater_than[0]',//婴儿价
            ],
            'flight_segments.*.product_no'     => [
                'label' => '产品编号',
                'rules' => 'required|min_length[1]|max_length[30]',//产品编号
            ],
            'passengers'                       => ['label' => '乘客信息', 'rules' => 'required|is_array'],
            // 'passengers.*.rph' => [
            //     'label' => '乘客编号',
            //     'rules' => 'required|greater_than[0]'
            // ],
            'passengers.*.name'                => [
                'label' => '乘客姓名',
                'rules' => 'required|min_length[2]|max_length[30]',
            ],
            'passengers.*.passenger_type_code' => [
                'label' => '乘客类型',
                'rules' => 'required|in_list[1,2,3]', //乘客类型：1成人 2儿童 3婴儿  对应接口：ADT成人 CHD儿童 INF婴儿
            ],
            'passengers.*.certificate_type'    => [
                'label' => '证件类型',
                'rules' => 'required|in_list[1,2]', //证件类型：1身份证 2护照  对应接口：NI 身份证 PP 护照
            ],
            'passengers.*.certificate_number'  => [
                'label' => '证件号码',
                'rules' => 'required|min_length[10]|max_length[50]',
            ],
            'passengers.*.contact_phone'       => [
                'label' => '乘客联系电话',
                'rules' => 'required|min_length[11]|max_length[20]',
            ],
            // 'passengers.*.infant_traveler_rph' => [
            //     'label' => '婴儿关联的成人乘客ID',
            //     'rules' => 'permit_empty|greater_than[0]'
            // ],
            'contact_name'                     => ['label' => '预订人姓名', 'rules' => 'required|max_length[6]|min_length[2]'],
            'contact_telephone'                => ['label' => '预订人联系电话', 'rules' => 'required|max_length[20]|min_length[11]'],
            'flight_type'                      => ['label' => '行程类型', 'rules' => 'required|in_list[1,2,3]'],//1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            //'product_insurance_id'             => ['label' => '保险', 'rules' => ''],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $validData['product_insurance_id'] = [1, 2];

        /** @var \App\Services\Order\Order $order_service */
        $order_service   = load_service('Order\Order');
        $insert_order_id = $order_service->create_order($validData);

        success('创建成功', ['order_id' => $insert_order_id]);
    }

    //确认订单
    public function confirmOrder()
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id  = intval($validData['order_id']);

        $airportModel = model('AirportModel');
        $airlineModel = model('AirlineModel');
        $flightModel  = model('FlightModel');
        $cabinModel   = model('CabinModel');

        $pnr_model           = model('PnrModel');
        $pnr_passenger_model = model('PnrPassengerModel');
        $order_segment_model = model('TicketBookSegModel');

        $order_model                          = model('TicketBookOrderModel');
        $order_detail_model                   = model('TicketBookOrderDetailModel');
        $order_passenger_model                = model('TicketBookPaxModel');
        $ticket_book_order_ticket_price_model = model('TicketBookOrderTicketPriceModel');

        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');
        //订单信息
        $order = $order_model->find($order_id);
        if (empty($order)) {
            error(0, 'order_id错误');
        }
        $pnr        = $pnr_model->find($order['pnr_id']);
        $order_data = [
            'order_no'          => $order['order_no'],//订单编号
            'pnr'               => $order['pnr'],
            'total_amount'      => $order['total_customer_amount'],//订单总额(总销售额)
            'ticket_time_limit' => $pnr['ticket_time_limit'] . '前',//出票时限
            'status'            => $order['status'],
            'status_text'       => OrderModel::ORDER_STATUS[$order['status']],
            'journey_type'      => $order['journey_type'],
            'journey_type_text' => OrderModel::JOURNEY_TYPES[$order['journey_type']],//航程类型
            'contact_name'      => $order['contact_name'],//联系人
            'contact_telephone' => $order['contact_telephone'],//联系电话
        ];
        //航班信息
        $order_segments = $order_segment_model->where('order_id', $order_id)->findAll();
        $flights        = $flightModel->whereIn('flight_number', array_column($order_segments, 'flight_number'))->findAll();//航班信息
        $flights        = array_column($flights, null, 'flight_number');

        $flight_data = [];
        foreach ($order_segments as $ps) {
            $flight       = $flights[$ps['flight_number']];
            $airline_code = $flight['airline_code'];//航司编号2位字母
            //出发/到达时间
            //时间间隔
            $interval = Tools\Time::compute_interval($flight['departure_time'], $flight['arrival_time']);
            $hours    = $interval['hours'] ? $interval['hours'] . 'h' : '';
            $minutes  = $interval['minutes'] ? $interval['minutes'] . 'm' : '';
            //航司logo
            $airline_logo = '/static/images/airline/' . $flight['airline_code'] . '.png';
            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airline_code, 'cabin' => $ps['cabin']])->first();
            //航程类型标识
            //航程类型：1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            switch ($order['journey_type']) {
                case 1://单程
                    $journey_name = '单程';
                    break;
                case 2:
                    if ($ps['rph'] == 1) {
                        $journey_name = '去程';
                    } else {
                        $journey_name = '返程';
                    }
                    break;
                case 3:
                case 4:
                case 5:
                    $journey_name = '第' . Tools\Char::to_chinase_num($ps['rph']) . '程';
                    break;
            }
            $flight_data[] = [
                'journey_name'         => $journey_name,
                'airline_logo'         => $airline_logo,
                'flight_number'        => $ps['flight_number'],
                'airline_code'         => $airline_code,//航司编号2位字母
                'airline_cn'           => $airlines[$airline_code]['airline_cn'],//航空公司名称
                'air_equip_type'       => $flight['air_equip_type'], //机型
                'meal'                 => '有餐食',//TODO:需要计算出来，此处暂写死
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                'arrival_airport_cn'   => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                'departure_time'       => $flight['departure_time'],
                'arrival_time'         => $flight['arrival_time'],
                'interval'             => $hours . $minutes,
                'cabin'                => $ps['cabin'], //舱位编号
                'discount'             => $cabin['discount'] . '折',//折扣
                'luggage_weight'       => $cabin['luggage_weight'],//行李重量
                'refund_rate'          => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin'            => $ps['sub_cabin'],//产品编号
                // 'adult_price' => 0,//成人价
                // 'children_price' => 0,//儿童价
                // 'baby_price' => 0,//婴儿价
                // 'tax_cn' => 0,//机场建设费
                // 'tax_yq' => 0,//燃油附加费
                // 'tax_xt' => 0,//其他税费
            ];
        }
        //乘机人信息
        $passenger_data   = [];
        $order_passengers = $order_passenger_model->where('order_id', $order_id)->findAll();
        foreach ($order_passengers as $pp) {
            $passenger_data[] = [
                'rph'                 => $pp['rph'],//序号
                'person_name'         => $pp['person_name'],//乘客姓名
                'passenger_type'      => $pp['passenger_type'],//乘客类型
                'passenger_type_text' => PnrPassengerModel::PASSENGER_TYPE[$pp['passenger_type']],//乘客类型文案
                'doc_type'            => $pp['doc_type'],//证件类型
                'doc_type_text'       => PnrPassengerModel::DOC_TYPE[$pp['doc_type']],//证件类型文案
                'doc_id'              => Tools\Idcard::mask($pp['doc_id']),//证件号码
                'telephone'           => Tools\Char::mask_phone($pp['telephone']),//联系电话
            ];
        }
        //价格信息
        $order_detail     = $order_detail_model->where('order_id', $order_id)->findAll();
        $order_passengers = array_column($order_passengers, null, 'id');

        $tmp = [];
        foreach ($order_detail as $dt) {
            $passenger_type         = $order_passengers[$dt['order_passenger_id']]['passenger_type'];
            $tmp[$passenger_type][] = $dt;
        }
        $price_data = [
            'detail' => [],
            'total'  => [
                'total_customer_price'  => 0.00,
                'total_tax_cn'          => 0.00,
                'total_tax_yq'          => 0.00,
                'total_tax_xt'          => 0.00,
                'total_customer_amount' => 0.00,
            ],
        ];
        foreach ($tmp as $key => $val) {
            $passenger_type                               = $key;
            $passenger_type_text                          = OrderPassengerModel::PASSENGER_TYPE[$passenger_type];
            $passenger_num                                = count($val);//该类型的人数
            $item                                         = $val[0];
            $customer_price                               = $item['ticket_marketing_price'] * $passenger_num;//销售价
            $tax_cn                                       = $item['ticket_tax_cn'] * $passenger_num;//机建机场建设费
            $tax_yq                                       = $item['ticket_tax_yq'] * $passenger_num; //燃油附加费
            $tax_xt                                       = $item['ticket_tax_xt'] * $passenger_num;//其他税费
            $customer_amount                              = $item['customer_amount'] * $passenger_num;//总销售金额
            $price_data['detail'][]                       = [
                'name'            => $passenger_type_text . ' * ' . $passenger_num,
                'customer_price'  => $customer_price,
                'tax_cn'          => $tax_cn,
                'tax_yq'          => $tax_yq,
                'tax_xt'          => $tax_xt,
                'customer_amount' => $customer_amount,
            ];
            $price_data['total']['total_customer_price']  = bcadd($price_data['total']['total_customer_price'], $customer_price, 2);
            $price_data['total']['total_tax_cn']          = bcadd($price_data['total']['total_tax_cn'], $tax_cn, 2);
            $price_data['total']['total_tax_yq']          = bcadd($price_data['total']['total_tax_yq'], $tax_yq, 2);
            $price_data['total']['total_tax_xt']          = bcadd($price_data['total']['total_tax_xt'], $tax_xt, 2);
            $price_data['total']['total_customer_amount'] = bcadd($price_data['total']['total_customer_amount'], $customer_amount, 2);
        }
        $data = [
            'order_data'     => $order_data,
            'flight_data'    => $flight_data,
            'passenger_data' => $passenger_data,
            'price_data'     => $price_data,
        ];

        success('获取成功', $data);
    }

    /**
     * @desc 国内订单列表
     *
     * <AUTHOR> 2025-06-07
     */
    public function orderList()
    {
        $validation = service('validation');
        $rules      = [
            'page'           => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page'       => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'pnr'            => ['label' => 'PNR编码', 'rules' => 'permit_empty|max_length[6]|min_length[3]'],
            'ticket_number'  => ['label' => '票号', 'rules' => 'permit_empty|max_length[20]|min_length[3]'],
            'person_name'    => ['label' => '乘客姓名', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_status'   => ['label' => '订单状态', 'rules' => 'permit_empty|greater_than_equal_to[0]'],//小于0则失败
            'journey_type'   => ['label' => '行程类型', 'rules' => 'permit_empty|in_list[1,2,3]'],//1单程2往返3多程
            'order_no'       => ['label' => '订单编号', 'rules' => 'permit_empty|max_length[20]|min_length[13]'],
            'airline'        => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'flight_number'  => ['label' => '航班号', 'rules' => 'permit_empty|max_length[10]|min_length[6]'],
            'customer_type'  => ['label' => '订单类型', 'rules' => 'permit_empty|in_list[1,2,3,4]'],// 订单类型：1自有 2分销
            'order_source'   => ['label' => '订单来源', 'rules' => 'permit_empty|in_list[1,2,3,4,5]'],//订单来源：1系统白屏预订 2分销白屏预订 3差旅白屏预订 4OTA订单 5B2B订单
            'operator_name'  => ['label' => '订票员', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_date'     => ['label' => '下单日期', 'rules' => 'permit_empty|is_array'],
            'departure_date' => ['label' => '出发日期', 'rules' => 'permit_empty|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /** @var \App\Services\Order\Order $orderService */
        $orderService = load_service('Order\Order');
        $data         = $orderService->domestic_book_order_list($validData);

        success('成功', $data);
    }

    //国内订单明细
    public function orderDetail()
    {
        $validation = service('validation');
        $rules      = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id  = intval($validData['order_id']);

        $airportModel          = model('AirportModel');
        $airlineModel          = model('AirlineModel');
        $order_model           = model('TicketBookOrderModel');
        $user_model            = model('UserModel');
        $department_model      = model('DepartmentModel');
        $order_segment_model   = model('TicketBookSegModel');
        $flightModel           = model('FlightModel');
        $cabinModel            = model('CabinModel');
        $order_passenger_model = model('TicketBookPaxModel');
        $order_detail_model    = model('TicketBookOrderDetailModel');
        $customerModel         = model('CustomerModel');
        $orderPaymentModel     = model('OrderPaymentModel');

        //订单信息
        $order = $order_model->where('id', $order_id)->where('area_type', 1)->first();
        if (empty($order)) {
            error(0, 'order_id错误');
        }
        $user       = $user_model->find($order['operator_id']);
        $department = $department_model->find($user['department_id']);

        // 客户信息
        $customer_name = '';
        $card_no = '';
        if (!empty($order['customer_id'])) {
            $customer_info = $customerModel->find($order['customer_id']);
            if (!empty($customer_info)) {
                $customer_name = $customer_info['customer_name'];
                $card_no = $customer_info['card_no'];
            }
        }

        // 收款信息
        $payment_type_text = '';
        $payment_time = '';
        $payment_info = $orderPaymentModel->where('order_type', 1)->where('order_id', $order_id)->orderBy('id', 'desc')->first();
        if (!empty($payment_info)) {
            $payment_type_text = OrderPaymentModel::PAYMENT_TYPE[$payment_info['payment_type']];
            $payment_time = date('Y-m-d H:i:s', $payment_info['created_at']);
        }

        $order_data = [
            'order_no'                   => $order['order_no'],//订单编号
            'status'                     => $order['status'],
            'status_text'                => OrderModel::ORDER_STATUS[$order['status']],
            'customer_payment_flag'      => $order['customer_payment_flag'],
            'customer_payment_flag_text' => OrderModel::CUSTOMER_PAYMENT_FLAG[$order['customer_payment_flag']],
            'pnr'                        => $order['pnr'],
            'old_pnr'                    => '',//旧pnr
            'total_supplier_amount'      => $order['total_supplier_amount'],//总采购金额
            'total_customer_amount'      => $order['total_customer_amount'],//订单总额(总销售额)
            'customer_type'              => OrderModel::CUSTOMER_TYPES[$order['customer_type']],//订单类型
            'customer_id'                => $order['customer_id'],
            'customer_name'              => $customer_name,
            'card_no'                    => $card_no,
            'payment_type_text'                    => $payment_type_text,
            'payment_time'                    => $payment_time,
            'order_source'               => OrderModel::ORDER_SOURCES[$order['order_source']],
            'created_at'                 => date('Y-m-d H:i:s', $order['created_at']),//下单时间
            'operator_name'              => $department['department_name'] . '|' . $user['name'],//操作员
            'journey_type'               => $order['journey_type'],//航程类型
            'journey_type_text'          => OrderModel::JOURNEY_TYPES[$order['journey_type']],//航程类型名称
        ];
        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');
        //航班信息
        $order_segments = $order_segment_model->where('order_id', $order_id)->findAll();
        $flights        = $flightModel->whereIn('flight_number', array_column($order_segments, 'flight_number'))->findAll();
        $flights        = array_column($flights, null, 'flight_number');

        $flight_data = [];
        foreach ($order_segments as $ps) {
            $flight       = $flights[$ps['flight_number']];
            $airline_code = $flight['airline_code'];//航司编号2位字母
            //航司logo
            $airline_logo = '';
            $airline_logo = '/static/images/airline/' . $flight['airline_code'] . '.png';
            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airline_code, 'cabin' => $ps['cabin']])->first();
            //航程类型标识
            //航程类型：1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            switch ($order['journey_type']) {
                case 1://单程
                    $journey_name = '单程';
                    break;
                case 2:
                    if ($ps['rph'] == 1) {
                        $journey_name = '去程';
                    } else {
                        $journey_name = '返程';
                    }
                    break;
                case 3:
                case 4:
                case 5:
                    $journey_name = '第' . Tools\Char::to_chinase_num($ps['rph']) . '程';
                    break;
            }
            $flight_data[] = [
                'journey_name'         => $journey_name,
                'airline_logo'         => $airline_logo,
                'flight_number'        => $ps['flight_number'],
                'airline_code'         => $airline_code,//航司编号2位字母
                'airline_cn'           => $airlines[$airline_code]['airline_cn'],//航空公司名称
                'air_equip_type'       => $flight['air_equip_type'], //机型
                'meal'                 => '有餐食',//TODO:需要计算出来，此处暂写死
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                'arrival_airport_cn'   => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                'departure_datetime'   => $ps['departure_datetime'],//出发时间
                'arrival_datetime'     => $ps['arrival_datetime'],//到达时间
                'cabin'                => $ps['cabin'], //舱位编号
                'discount'             => $cabin['discount'] . '折',//折扣
                'luggage_weight'       => $cabin['luggage_weight'],//行李重量
                //'refund_rate' => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin'            => $ps['sub_cabin'],//产品编号
                'customer_explain'     => '退票规定:起飞前7天《含)之前:Q舱的20%，起飞前7天(不含)之内至起飞前2天(含)之前:Q舱的30%，起飞前2天(不含)之内至起飞前4小时……',//客规说明
            ];
        }
        //乘机人信息
        $passenger_data      = [];
        $order_passengers    = $order_passenger_model->where('order_id', $order_id)->findAll();
        $order_passengers    = array_column($order_passengers, null, 'id');
        foreach ($order_passengers as $pp) {
            $order_passenger_id = $pp['id'];
            $ticket_number      = $pp['ticket_number'];
            $ticket_number      = $ticket_number ? Tools\Char::mask_ticket($ticket_number) : '';
            $order_details       = $order_detail_model->where('order_passenger_id', $order_passenger_id)->findAll();
            $detail_ticket = [];
            $detail_insurance = [];
            foreach ($order_details as $item) {
                if ($item['product_type'] == 1) {
                    $detail_ticket = $item;
                } else if ($item['product_type'] == 2) {
                    $detail_insurance[] = $item;
                }
            }

            $ticket_total_price = 0.00; //票价总价
            $supplier_amount    = 0.00;//采购总计
            $customer_amount    = 0.00;//销售总计
            $insurance_marketing_price = 0.00;

            // 票面总价=票面价+机场建设费+燃油附加费
            $ticket_total_price = bcadd($ticket_total_price, $detail_ticket['ticket_marketing_price'], 2);          // 加票面价
            $ticket_total_price = bcadd($ticket_total_price, $detail_ticket['ticket_tax_cn'], 2);           // 加机场建设费
            $ticket_total_price = bcadd($ticket_total_price, $detail_ticket['ticket_tax_yq'], 2);           // 加燃油附加费

            // 采购总计=票面总价-代理费+采购服务费+保险
            $supplier_amount = bcadd($supplier_amount, $ticket_total_price, 2);              // 加总票面价
            $supplier_amount = bcsub($supplier_amount, $detail_ticket['ticket_supplier_agency_fee'], 2);      // 减代理费
            $supplier_amount = bcadd($supplier_amount, $detail_ticket['ticket_supplier_service_fee'], 2);     // 加采购服务费
            if (!empty($detail_insurance)) {
                foreach ($detail_insurance as $item) {
                    $supplier_amount = bcadd($supplier_amount, $item['insurance_supplier_price'], 2);           // 加保险
                }
            }
            
            //销售总计=票面价-让利价+销售服务费+保险
            $customer_amount = bcadd($customer_amount, $ticket_total_price, 2);          // 加总票面价
            $customer_amount = bcsub($customer_amount, $detail_ticket['ticket_customer_adjust_fee'], 2);  // 加加价/让利
            $customer_amount = bcadd($customer_amount, $detail_ticket['ticket_customer_service_fee'], 2); // 加销售服务费
            if (!empty($detail_insurance)) {
                foreach ($detail_insurance as $item) {
                    $customer_amount = bcadd($customer_amount, $item['insurance_customer_price'], 2);           // 加保险
                    $insurance_marketing_price = bcadd($insurance_marketing_price, $item['insurance_customer_price'], 2);
                }
            }

            $passenger_data[] = [
                'order_id'                    => $order_id,
                'passenger_id'                => $pp['id'],
                'rph'                         => $pp['rph'],//序号
                'person_name'                 => $pp['person_name'],//乘客姓名
                'passenger_type'              => $pp['passenger_type'],//乘客类型
                'passenger_type_text'         => PnrPassengerModel::PASSENGER_TYPE[$pp['passenger_type']],//乘客类型文案
                'doc_type'                    => $pp['doc_type'],//证件类型
                'doc_type_text'               => PnrPassengerModel::DOC_TYPE[$pp['doc_type']],//证件类型文案
                'doc_id'                      => Tools\Idcard::mask($pp['doc_id']),//证件号码
                'telephone'                   => Tools\Char::mask_phone($pp['telephone']),//联系电话
                'ticket_number'               => $ticket_number,//票号
                'is_free'                     => $detail_ticket['is_free'],//是否免费票：0否 1是
                'ticket_marketing_price'      => $detail_ticket['ticket_marketing_price'],//票价
                'ticket_tax_cn'               => $detail_ticket['ticket_tax_cn'],//机场建设费
                'ticket_tax_yq'               => $detail_ticket['ticket_tax_yq'],//燃油附加费
                'ticket_tax_xt'               => $detail_ticket['ticket_tax_xt'],//其他税费
                'ticket_total_price'          => $detail_ticket['ticket_total_price'],//票面总价
                'ticket_supplier_agency_fee'  => $detail_ticket['ticket_supplier_agency_fee'],//代理费
                'ticket_supplier_service_fee' => $detail_ticket['ticket_supplier_service_fee'],
                'ticket_customer_adjust_fee'  => $detail_ticket['ticket_customer_adjust_fee'],
                'ticket_customer_service_fee' => $detail_ticket['ticket_customer_service_fee'],
                'insurance_marketing_price'   => $insurance_marketing_price,//保险费
                'insurance_marketing_bonus'   => 0,//保险奖励
                'supplier_amount'             => $supplier_amount,//采购总计
                'customer_amount'             => $customer_amount,//销售总计
            ];
        }
        $data = [
            'order_data'     => $order_data,
            'flight_data'    => $flight_data,
            'passenger_data' => $passenger_data,
        ];

        success('获取成功', $data);
    }

    //保存国内出票订单价格
    public function savePrice()
    {
        $validation = service('validation');
        $rules      = [
            'order_id'                                 => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
            'passengers'                               => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'passengers.*.passenger_id'                => ['label' => '乘客ID', 'rules' => 'required|greater_than[0]'],
            'passengers.*.is_free'                     => ['label' => '是否免票', 'rules' => 'required|in_list[0,1]'],
            'passengers.*.ticket_marketing_price'      => ['label' => '票面价', 'rules' => 'required|decimal'],
            'passengers.*.ticket_tax_cn'               => ['label' => '机场建设费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_tax_yq'               => ['label' => '税费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_supplier_agency_fee'  => ['label' => '代理费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_supplier_service_fee' => ['label' => '采购服务费', 'rules' => 'required|decimal'],
            'passengers.*.ticket_customer_adjust_fee'  => ['label' => '销售加价/减价', 'rules' => 'required|decimal'],
            'passengers.*.ticket_customer_service_fee' => ['label' => '销售服务费', 'rules' => 'required|decimal'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        /**
         * @var \App\Services\Order\Order $service
         */
        $service = load_service('Order\Order');
        $data    = $service->saveDomesticBookPrice($validData);

        success('成功', $data);
    }

    /**
     * @desc 设置订单会员ID
     *
     * <AUTHOR> 2025-06-24
     */
    public function setCustomerId()
    {
        $validation = service('validation');
        $rules      = [
            'order_type' => ['label' => '订单类型', 'rules' => 'required|in_list[1,2]'],//订单类型：1出票订单 2改签订单
            'order_id'   => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

    }

    /**
     * 手动出票接口
     * 用于手动录入机票订单的出票信息
     *
     * @return void
     */
    public function manualIssue(): void
    {
        $validation = service('validation');
        $rules      = [
            // 航段信息
            'segments'                          => ['label' => '航段信息', 'rules' => 'required|is_array'],
            'segments.*.flight_number'          => ['label' => '航班号', 'rules' => 'required|min_length[6]|max_length[10]'],
            'segments.*.departure_datetime'     => ['label' => '出发时间', 'rules' => 'required|valid_date[Y-m-d H:i:s]'],
            'segments.*.departure_airport'      => ['label' => '出发机场', 'rules' => 'required|exact_length[3]'],
            'segments.*.arrival_datetime'       => ['label' => '到达时间', 'rules' => 'required|valid_date[Y-m-d H:i:s]'],
            'segments.*.arrival_airport'        => ['label' => '到达机场', 'rules' => 'required|exact_length[3]'],
            'segments.*.departure_terminal'     => ['label' => '出发航站楼', 'rules' => 'permit_empty|max_length[10]'],
            'segments.*.arrival_terminal'       => ['label' => '到达航站楼', 'rules' => 'permit_empty|max_length[10]'],
            'segments.*.cabin'                  => ['label' => '舱位', 'rules' => 'required|max_length[5]'],

            // 乘客信息
            'passengers'                        => ['label' => '乘客信息', 'rules' => 'required|is_array'],
            'passengers.*.person_name'          => ['label' => '乘客姓名', 'rules' => 'required|max_length[50]'],
            'passengers.*.doc_id'               => ['label' => '证件号', 'rules' => 'required|max_length[30]'],
            'passengers.*.telephone'            => ['label' => '手机号', 'rules' => 'required|max_length[20]'],
            'passengers.*.passenger_type'       => ['label' => '乘客类型', 'rules' => 'required|in_list[1,2,3]'], // 1成人 2儿童 3婴儿
            'passengers.*.ticket_price'         => ['label' => '票面价', 'rules' => 'required|decimal'],
            'passengers.*.tax_cn'               => ['label' => '机建费', 'rules' => 'permit_empty|decimal'],
            'passengers.*.tax_yq'               => ['label' => '燃油费', 'rules' => 'permit_empty|decimal'],
            'passengers.*.tax_xt'               => ['label' => '其他税费', 'rules' => 'permit_empty|decimal'],
            'passengers.*.agency_fee'           => ['label' => '代理费', 'rules' => 'permit_empty|decimal'],
            'passengers.*.supplier_service_fee' => ['label' => '采购服务费', 'rules' => 'permit_empty|decimal'],
            'passengers.*.customer_service_fee' => ['label' => '销售服务费', 'rules' => 'permit_empty|decimal'],
            'passengers.*.adjust_fee'           => ['label' => '加价/让利', 'rules' => 'permit_empty|decimal'],
            'passengers.*.insurance_fee'        => ['label' => '保险费', 'rules' => 'permit_empty|decimal'],

            // 联系人信息
            'contact_name'                      => ['label' => '联系人姓名', 'rules' => 'required|max_length[50]'],
            'contact_telephone'                 => ['label' => '联系人手机', 'rules' => 'required|max_length[20]'],
            'contact_email'                     => ['label' => '联系人邮箱', 'rules' => 'permit_empty|valid_email|max_length[100]'],
        ];

        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }

        $validData = $validation->getValidated();
        // 默认是国内，如果是国际的，后面会修改该字段
        $validData['area_type'] = 1;
        try {
            /**
             * @var \App\Services\Order\Manual $service
             */
            $service = load_service('Order\Manual');
            $orderId = $service->processManualIssue($validData);
            success('手动出票成功', ['order_id' => $orderId]);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
    }

    /**
     * 拉取订单
     *
     * @return void
     */
    public function pullOrder(): void
    {
        $validation = service('validation');
        $rules      = [
            'ticket_number' => ['label' => '票号', 'rules' => 'permit_empty|min_length[2]'],
            'pnr'           => ['label' => 'PNR', 'rules' => 'permit_empty|min_length[2]'],
            'start_time'    => ['label' => '开始时间', 'rules' => 'permit_empty|valid_date'],
            'end_time'      => ['label' => '结束时间', 'rules' => 'permit_empty|valid_date'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        try {
            // 创建PNR数据拉取服务实例
            $pullService = new PullPnrDataService();

            // 获取参数
            $ticketNumber = $validData['ticket_number'] ?? null;
            $pnr          = $validData['pnr'] ?? null;

            // 处理时间参数，未传时默认当天0点到23:59:59
            $startTime = $validData['start_time'] ?? null;
            $endTime   = $validData['end_time'] ?? null;
            if (empty($startTime) && empty($endTime)) {
                $startTime = date('Y-m-d 00:00:00');
                $endTime   = date('Y-m-d 23:59:59');
            } elseif (empty($startTime) && !empty($endTime)) {
                $startTime = date('Y-m-d 00:00:00', strtotime($endTime));
            } elseif (!empty($startTime) && empty($endTime)) {
                $endTime = date('Y-m-d 23:59:59', strtotime($startTime));
            }

            // 解析时间参数
            $timeParams = $pullService->parseTimeParams([$startTime, $endTime]);
            $startTime  = $timeParams['start_time'];
            $endTime    = $timeParams['end_time'];

            // 调用服务获取数据（不保存到数据库）
            $result = $pullService->pullDataOnly($startTime, $endTime, $ticketNumber, $pnr);

            if (!empty($result['errors'])) {
                throw new \Exception(json_encode($result['errors']));
            }

            success('获取数据成功', $result['orders']);

        } catch (\Exception $e) {
            log_message('error', '拉取订单数据失败: ' . $e->getMessage());
            error(500, '拉取数据失败: ' . $e->getMessage());
        }
    }
}