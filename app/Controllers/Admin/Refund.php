<?php

namespace App\Controllers\Admin;

use App\Controllers\AdminController;
use App\Helpers\Tools;
use App\Models\PnrPassengerModel;
use App\Models\OrderModel;
use App\Models\OrderPassengerModel;
use App\Models\TicketBookOrderModel;
use App\Services\PnrData\PullPnrDataService;
use App\Models\PnrTicketSegmentModel;
use App\Models\CabinModel;

class Refund extends AdminController
{
    //国内退票订单列表
    public function orderList()
    {
        $validation = service('validation');
        $rules      = [
            'page'            => ['label' => '页数', 'rules' => 'permit_empty|greater_than[0]'],
            'per_page'        => ['label' => '每页显示数量', 'rules' => 'permit_empty|greater_than[0]'],
            'pnr'             => ['label' => 'PNR编码', 'rules' => 'permit_empty|max_length[6]|min_length[3]'],
            'order_no'        => ['label' => '改签单号', 'rules' => 'permit_empty|max_length[20]|min_length[3]'],
            'person_name'     => ['label' => '乘客姓名', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_status'    => ['label' => '订单状态', 'rules' => 'permit_empty|greater_than_equal_to[0]'],//小于0则失败
            'origin_order_no' => ['label' => '原订单编号', 'rules' => 'permit_empty|max_length[20]|min_length[13]'],
            'airline'         => ['label' => '航空公司', 'rules' => 'permit_empty|max_length[2]|min_length[2]'],
            'operator_name'   => ['label' => '订票员', 'rules' => 'permit_empty|max_length[6]|min_length[1]'],
            'order_date'      => ['label' => '退票日期', 'rules' => 'permit_empty|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJson(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        $refundService = load_service('RefundService');
        $data          = $refundService->orderList($validData);

        success('成功', $data);
    }

    //国内退票申请
    public function apply()
    {
        $validation = service('validation');
        $rules = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (! $validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);
        $order_model = model('TicketBookOrderModel');
        $order_passenger_model = model('TicketBookPaxModel');
        $order_segment_model = model('TicketBookSegModel');
        $order_passenger_segment_model = model('TicketBookPaxSegModel');
        $user_model = model('UserModel');
        $department_model = model('DepartmentModel');
        $pnr_ticket_model = model('PnrTicketModel');
        $pnr_ticket_segment_model = model('PnrTicketSegmentModel');
        $airportModel = model('AirportModel');
        $airlineModel = model('AirlineModel');
        $cabinModel = model('CabinModel');
        $flightModel = model('FlightModel');
        $order_detail_model = model('OrderDetailModel');

        //订单信息
        $order = $order_model->find($order_id);
        if (empty($order)) {
            error(0, 'order_id错误');
        }
        if (!in_array($order['status'], [2,3])) {
            error(0, '只有【已出票】【部分出票】状态才可申请退票');
        }
        $user = $user_model->find($order['operator_id']);
        $department = $department_model->find($user['department_id']);
        $order_data = [
            'order_no' => $order['order_no'],//订单编号
            'pnr' => $order['pnr'],
            'customer_type' => OrderModel::CUSTOMER_TYPES[$order['customer_type']],//订单类型
            'created_at' => date('Y-m-d H:i:s', $order['created_at']),//下单时间
            'operator_name' => $department['department_name'] . '|' . $user['name'],//操作员
            'journey_type' => $order['journey_type'],//航程类型
            'journey_type_text' => OrderModel::JOURNEY_TYPES[$order['journey_type']],//航程类型名称
        ];

        //乘客信息
        $passenger_data = [];
        $order_passengers = $order_passenger_model->where('order_id', $order_id)->findAll();
        $order_passenger_ids = array_column($order_passengers, 'id');

        $order_ticket_segment_arr = [];//按ticket_id分组
        $allow_flight_numbers = [];//允许退票的航班
        $order_passenger_segments = $order_passenger_segment_model->whereIn('passenger_id', $order_passenger_ids)->findAll();
        foreach ($order_passenger_segments as $ps) {
            $ticket_id = $ps['passenger_id'];
            $order_ticket_segment_arr[$ticket_id][] = [
                'ticket_id' => $ticket_id,
                'flight_number' => $ps['flight_number'],
                'status' => $ps['status'],//只有在1时可用
                'status_text' => PnrTicketSegmentModel::STATUS[$ps['status']],
            ];

            if ($ps['status'] == 1) {
                $allow_flight_numbers[$ticket_id][] = $ps['flight_number'];
            }
        }
        foreach ($order_passengers as $pp) {
            $ticket_number = $pp['ticket_number'];
            $ticket_id = $pp['id'];
            /**
                【退改状态】 计算规则：
                1.只要有一张状态为 【1.可使用(OPEN FOR USE)】 则退改状态为：可退票
                2.一张 可使用 都没有，则为 不可退
                3.全部都是 【5.已退票(REFUNDED)】    则为 已退票
             **/
            //电子票号对应航段数组
            $ticket_segments = $order_ticket_segment_arr[$ticket_id];
            $available_qty = 0;//可退票数量
            $refunded_qyt = 0;//已退票数
            foreach ($ticket_segments as $ts) {
                if ($ts['status'] == 1) {
                    $available_qty += 1;
                } elseif ($ts['status'] == 5) {
                    $refunded_qyt += 1;
                }
            }
            if ($available_qty >= 1) {
                $refund_change_status = '可退票';
            } elseif ($refunded_qyt == count($ticket_segments)) {
                $refund_change_status = '已退票';
            } else {
                $refund_change_status = '不可退';
            }
            $passenger_data[] = [
                'rph' => $pp['rph'],//序号
                'ticket_id' => $ticket_id,
                'pnr_passenger_id' => $pp['id'],
                'order_id' => $order_id,
                'person_name' => $pp['person_name'],//乘客姓名
                'gender' => PnrPassengerModel::GENDER[$pp['gender']],//性别
                'passenger_type' => $pp['passenger_type'],//乘客类型
                'passenger_type_text' => PnrPassengerModel::PASSENGER_TYPE[$pp['passenger_type']],//乘客类型文案
                'doc_type' => $pp['doc_type'],//证件类型
                'doc_type_text' => PnrPassengerModel::DOC_TYPE[$pp['doc_type']],//证件类型文案
                'doc_id' => Tools\Idcard::mask($pp['doc_id']),//证件号码
                'telephone' => Tools\Char::mask_phone($pp['telephone']),//联系电话
                'ticket_number' => Tools\Char::mask_ticket($ticket_number),//票号
                'ticket_segments' => $ticket_segments,//电子票号对应航段数组
                'allow_flight_numbers' => $allow_flight_numbers[$ticket_id] ?? [],
                'refund_change_status' => $refund_change_status//退改状态
            ];
        }
        //航班信息
        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');

        $order_segments = $order_segment_model->where('order_id', $order_id)->findAll();
        $flights = $flightModel->whereIn('flight_number', array_column($order_segments, 'flight_number'))->findAll();
        $flights = array_column($flights, null, 'flight_number');

        $flight_data = [];
        foreach ($order_segments as $ps) {
            $flight = $flights[$ps['flight_number']];
            $airline_code = $flight['airline_code'];//航司编号2位字母
            //航司logo
            $airline_logo = '';
            $airline_logo = '/static/images/airline/' . $flight['airline_code'] . '.png';
            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airline_code, 'cabin' => $ps['cabin']])->first();
            //航程类型标识
            //航程类型：1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            switch ($order['journey_type']) {
                case 1://单程
                    $journey_name = '单程';
                    break;
                case 2:
                    if ($ps['rph'] == 1) {
                        $journey_name = '去程';
                    } else {
                        $journey_name = '返程';
                    }
                    break;
                case 3:
                case 4:
                case 5:
                    $journey_name = '第' . Tools\Char::to_chinase_num($ps['rph']) . '程';
                    break;
            }
            $flight_data[] = [
                'journey_name' => $journey_name,
                'airline_logo' => $airline_logo,
                'flight_number' => $ps['flight_number'],
                'airline_code' => $airline_code,//航司编号2位字母
                'airline_cn' => $airlines[$airline_code]['airline_cn'],//航空公司名称
                'air_equip_type' => $flight['air_equip_type'], //机型
                'meal' => '有餐食',//TODO:需要计算出来，此处暂写死
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                'arrival_airport_cn' => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                'departure_datetime' => $ps['departure_datetime'],//出发时间
                'arrival_datetime' => $ps['arrival_datetime'],//到达时间
                'cabin' => $ps['cabin'], //舱位编号
                'discount' => $cabin['discount'] . '折',//折扣
                'luggage_weight' => $cabin['luggage_weight'],//行李重量
                //'refund_rate' => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin' => $ps['sub_cabin'],//产品编号
                'customer_explain' => '退票规定:起飞前7天《含)之前:Q舱的20%，起飞前7天(不含)之内至起飞前2天(含)之前:Q舱的30%，起飞前2天(不含)之内至起飞前4小时……'//客规说明
            ];
        }

        $data = [
            'order_data' => $order_data,
            'flight_data' => $flight_data,
            'passenger_data' => $passenger_data,
        ];

        success('获取成功', $data);
    }

    //查询国内退款费用
    public function queryRefundFee()
    {
        $validation = service('validation');
        $rules = [
            'ticket_ids' => ['label' => '票号ids', 'rules' => 'required|is_array'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $ticket_ids = $validData['ticket_ids'];
        $ticket_service = load_service('Ticket\Ticket');
        //$order_passenger_model = model('TicketBookPaxModel');
        try {
            //查询价格
            $data = $ticket_service->query_ticket_price($ticket_ids);
        } catch (\Exception $e) {
            error(0, $e->getMessage());
        }
        success('获取成功', $data);
    }

    //国内确认退票
    public function confirmRefund()
    {
        $validation = service('validation');
        $rules = [
            'tickets' => ['label' => '票号信息', 'rules' => 'required|is_array'],
            'tickets.*.ticket_id' => [
                'label' => '票号id',
                'rules' => 'required|greater_than[0]'
            ],
            'tickets.*.service_fee' => [
                'label' => '退票手续费',
                'rules' => 'required|greater_than_equal_to[0]'
            ],
            'tickets.*.deduction' => [
                'label' => '退票费',
                'rules' => 'required|greater_than_equal_to[0]'
            ],
            'order_id' => ['label' => '订单id', 'rules' => 'required|greater_than[0]'],
            'refund_type' => ['label' => '退票原因', 'rules' => 'required|in_list[1,2]'],//退票类型：1自愿退票 2非自愿退票
            'contact_name' => ['label' => '联系人', 'rules' => 'required|max_length[6]|min_length[2]'],
            'contact_telephone' => ['label' => '联系电话', 'rules' => 'required|max_length[20]|min_length[11]'],
            'contact_email' => ['label' => '联系邮箱', 'rules' => 'required|max_length[30]|min_length[6]'],
            'is_send_sms' => ['label' => '通知类型', 'rules' => 'required|in_list[0,1]'],//发送短信：0否 1是
            'remark' => ['label' => '备注', 'rules' => 'required|max_length[50]|min_length[2]']
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getPost())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);
        $refund_type = intval($validData['refund_type']);
        $is_send_sms = intval($validData['is_send_sms']);
        $contact_name = trim($validData['contact_name']);
        $contact_email = trim($validData['contact_email']);
        $contact_telephone = trim($validData['contact_telephone']);
        $remark = trim($validData['remark']);

        $post_tickets = $validData['tickets'];
        $post_tickets = array_column($post_tickets, null,'ticket_id');
        $ticket_ids = array_column($post_tickets, 'ticket_id');

        $order_passenger_segment_model = model('TicketBookPaxSegModel');
        $refund_order_passenger_segment_model = model('TicketRefundPaxSegModel');
        $order_passenger_model = model('TicketBookPaxModel');
        $refund_pax_model = model('TicketRefundPaxModel');
        $order_model = model('TicketBookOrderModel');
        $refund_order_model = model('TicketRefundOrderModel');
        $order_segment_model = model('TicketBookSegModel');
        $refund_seg_model = model('TicketRefundSegModel');
        $order_detail_model = model('TicketBookOrderDetailModel');
        $refund_order_detail_model = model('TicketRefundOrderDetailModel');
        $pnr_model = model('PnrModel');
        $ticket_service = load_service('Ticket\Ticket');

        //检票电子客票状态
        $order_passengers = $order_passenger_model->whereIn('id', $ticket_ids)->where('order_id', $order_id)->where('status', 1)->findAll();
        if (count($order_passengers) != count($ticket_ids)) {
            error(0, '票号状态有变动，请刷新页面后重试');
        }

        //校验电子客票航段状态
        $order_passenger_segments = $order_passenger_segment_model->whereIn('passenger_id', $ticket_ids)->where('status', 1)->findAll();
        if (empty($order_passenger_segments)) {
            error(0, '票号状态有变动，票号状态是【可使用(OPEN FOR USE)】才允许退票');
        }
        $allow_count = count(array_unique(array_column($order_passenger_segments, 'passenger_id')));
        if ($allow_count != count($ticket_ids)) {
            error(0, '票号状态有变动，请刷新页面后重试');
        }

        // 获取出票订单总预订人数
        $tmp_passenger = $order_passenger_model->where('id', $order_passenger_segments[0]['passenger_id'])->first();
        $order = $order_model->where('id', $tmp_passenger['order_id'])->first();
        $passenger_number = $order['passenger_number'];

        $order_passenger = $order_passenger_model->where('id', $ticket_ids)->findAll();

        $db = db_connect();
        //1.比对退票费价格（调用查询退票费接口）
        try {
            // 创建分离PNR用于退票（部分退需要分离PNR，全部退不需要）、
            $is_split_pnr = false;
            if ($passenger_number > $allow_count) {
                $is_split_pnr = true;
                $split_passenger = [];
                foreach ($order_passenger as $passenger) {
                    $arr_passenger[] = [
                        'passenger_type_code' => PnrPassengerModel::PASSENGER_TYPE_MAP[$passenger['passenger_type']],
                        'language_type' => 'ZH',
                        'surname' => $passenger['person_name'],
                        'doc' => [
                            'doc_type' => 'NI',
                            'doc_id' => $passenger['doc_id'],
                        ],
                        'rph' => $passenger['rph'],
                        'accompanied_by_infant' => false,
                    ];
                }
                $split_params = [
                    'pnr' => $order['pnr'],
                    'passengers' => $arr_passenger,
                ];
                $booking = new \App\Libraries\Api\IBE\Booking();
                $pnr = $booking->split_pnr($split_params);

                $pnr_info = $pnr_model->where('order_id', $order['id'])->first();
                $pnr_info['origin_pnr'] = $pnr_info['pnr'];
                $pnr_info['pnr'] = $pnr;
                $pnr_info['passenger_number'] = $allow_count;
                $pnr_info['created_at'] = time();
                $pnr_info['updated_at'] = time();
                unset($pnr_info['id']);
                $pnr_id = $pnr_model->insert($pnr_info);
            }

            //1.查询退票费用
            $query_res = $ticket_service->query_refund_fee($ticket_ids, 'DOMESTIC');
            //提交上来的价格，和接口返回的最新价格进行比对
            foreach ($query_res as $val) {
                $ticket_id = $val['ticket_id'];
                if ($post_tickets[$ticket_id]['deduction'] != $val['deduction']) {
                    error(0, "退票费不一致,页面提交：{$post_tickets[$ticket_id]['deduction']},而接口返回的：{$val['deduction']}");
                }
            }

            //2.退票
            $refund_res = $ticket_service->refund_ticket($query_res);
            if (empty($refund_res)) {
                error(0, '退票失败');
            }
            $passenger_ids = array_column($refund_res, 'ticket_id');
            $count_passenger = count($passenger_ids);//乘客数量
            $refund_res = array_column($refund_res, null,'ticket_id');

            $order_passengers = $order_passenger_model->whereIn('id', $passenger_ids)->findAll();
            $passenger_names = implode(',',array_column($order_passengers, 'person_name'));//乘客姓名，用逗号连接

            $order_passengers = array_column($order_passengers,null,'id');
            $order_detail = $order_detail_model->whereIn('order_passenger_id', $passenger_ids)->findAll();
            $old_order_id = $order_detail[0]['order_id'];

            //旧订单信息
            $old_order = $order_model->find($old_order_id);
            //采购价总额(各类型乘客的价格总额)
            $total_supplier_price = 0;
            //总采购金额(采购价总额+税费总额)
            $total_supplier_amount = 0;
            $total_customer_amount = 0;
            //各税费总额
            $total_tax = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];

            //生成订单号
            $new_order_no = $order_model->generate_order_no('R');
            $add_order_detail = [];
            foreach ($order_detail as $val) {
                $total_supplier_price = 0;

                $order_passenger_id = $val['order_passenger_id'];
                $interface_refund = $refund_res[$order_passenger_id];//接口返回的
                //采购价 = 原采购价 - 退票费 - 手续费(默认0)
                $supplier_price = bcsub($val['ticket_supplier_price'], $interface_refund['deduction'], 2);
                $supplier_price = bcsub($supplier_price, 0, 2);

                $total_supplier_price = bcadd($total_supplier_price, $supplier_price, 2);
                //税费
                $tax_arr = ['CN' => 0.00, 'YQ' => 0.00, 'XT' => 0.00];
                foreach ($interface_refund['taxes'] as $key1 => $val1) {
                    $tax_arr[$val1['tax_code']] = $val1['amount'];
                    $total_tax[$val1['tax_code']] = bcadd($total_tax[$val1['tax_code']], $val1['amount'], 2);
                }
                $deduction_fee_rate = bcdiv($interface_refund['deduction'], $interface_refund['gross_refund'], 2);
                $supplier_amount = 0;
                $supplier_amount = bcadd($supplier_amount, -$val['ticket_marketing_price'], 2);
                $supplier_amount = bcadd($supplier_amount, -$val['ticket_tax_cn'], 2);
                $supplier_amount = bcadd($supplier_amount, -$val['ticket_tax_yq'], 2);
                $supplier_amount = bcsub($supplier_amount, -$val['ticket_supplier_agency_fee'], 2);
                $supplier_amount = bcadd($supplier_amount, -$val['ticket_supplier_service_fee'], 2);
                $supplier_amount = bcadd($supplier_amount, -$val['insurance_marketing_price'], 2);
                $supplier_amount = bcadd($supplier_amount, $interface_refund['deduction'], 2);
                $customer_amount = 0;
                $customer_amount = bcadd($customer_amount, -$val['ticket_marketing_price'], 2);
                $customer_amount = bcadd($customer_amount, -$val['ticket_tax_cn'], 2);
                $customer_amount = bcadd($customer_amount, -$val['ticket_tax_yq'], 2);
                $customer_amount = bcadd($customer_amount, -$val['ticket_customer_adjust_fee'], 2);
                $customer_amount = bcadd($customer_amount, -$val['ticket_customer_service_fee'], 2);
                $customer_amount = bcadd($customer_amount, -$val['insurance_marketing_price'], 2);
                $customer_amount = bcadd($customer_amount, $interface_refund['deduction'], 2);
                $total_supplier_amount = bcadd($total_supplier_amount, $supplier_amount, 2);
                $total_customer_amount = bcadd($total_customer_amount, $customer_amount, 2);
                $add_order_detail[] = [
                    'origin_order_type' => 2,
                    'origin_order_id' => $old_order_id,
                    'origin_order_detail_id' => $val['id'],
                    'order_passenger_id' => $val['order_passenger_id'],
                    'ticket_supplier_marketing_price' => -$val['ticket_marketing_price'],
                    'ticket_supplier_tax_cn' => -$val['ticket_tax_cn'],
                    'ticket_supplier_tax_yq' => -$val['ticket_tax_yq'],
                    'ticket_supplier_agency_fee' => -$val['ticket_supplier_agency_fee'],
                    'ticket_supplier_service_fee' => -$val['ticket_supplier_service_fee'],
                    'ticket_supplier_insurance_fee' => -$val['insurance_marketing_price'],
                    'ticket_customer_marketing_price' => -$val['ticket_marketing_price'],
                    'ticket_customer_tax_cn' => -$val['ticket_tax_cn'],
                    'ticket_customer_tax_yq' => -$val['ticket_tax_yq'],
                    'ticket_customer_adjust_fee' => -$val['ticket_customer_adjust_fee'],
                    'ticket_customer_service_fee' => -$val['ticket_customer_service_fee'],
                    'ticket_customer_insurance_fee' => -$val['insurance_marketing_price'],

                    'ticket_supplier_deduction_fee' => $interface_refund['deduction'],
                    'ticket_supplier_deduction_fee_rate' => $deduction_fee_rate,
                    'ticket_customer_deduction_fee' => $interface_refund['deduction'],
                    'ticket_customer_deduction_fee_rate' => $deduction_fee_rate,

                    'supplier_amount' => $supplier_amount,//总采购金额
                    'customer_amount' => $customer_amount,//总销售金额
                    'origin_price' => $interface_refund['gross_refund'],
                    'origin_tax_cn' => $val['ticket_tax_cn'],
                    'origin_tax_yq' => $val['ticket_tax_yq'],
                    'origin_deduction_fee' => $interface_refund['deduction'],
                    'origin_deduction_fee_rate' => $deduction_fee_rate,//退票费率
                ];
            }
            $db->transException(true)->transStart();
            //3.记录数据库
            //3.1 记录order表
            $insert_order_id = $refund_order_model->insert([
                'order_no'  => $new_order_no,
                'origin_order_type' => 1,
                'origin_order_id' => $old_order['id'],
                'origin_order_no' => $old_order['order_no'],
                'refund_type' => $refund_type,
                'ticket_type'  => 1,
                'order_source'  => 1,
                'area_type'  => TicketBookOrderModel::ORDER_AREA_DOMESTIC,
                'customer_type'  => 1,
                'customer_id'  => 0,//客户ID，包括直销会员和分销会员
                'pnr' => $is_split_pnr ? $pnr : $old_order['pnr'],
                'pnr_id' => $is_split_pnr ? $pnr_id : $old_order['pnr_id'],
                'journey_type' => $old_order['journey_type'],//航程类型
                'journey_info' => $old_order['journey_info'],//航程
                'passenger_number' => $count_passenger,//旅客人数
                'passenger_names' => $passenger_names,//旅客姓名
                'total_supplier_amount' => $total_supplier_amount,//总采购金额
                'total_customer_amount' => $total_customer_amount,//总销售金额
                'office' => config('IBE')->office,//OFFICE号
                'status' => 2,//已完成
                'contact_name' => $contact_name,//联系人
                'contact_telephone' => $contact_telephone,//联系电话
                'contact_email' => $contact_email,//联系邮箱
                'is_send_sms' => $is_send_sms,
                'remark' => $remark,
                'operator_id' => 1,//TODO 因登录前端还没接，先暂时写死
                'operator_name' => '测试账号',//TODO 因登录前端还没接，先暂时写死
            ]);
            if (empty($insert_order_id)) {
                throw new \Exception('添加订单失败');
            }
            //3.2 记录order_detail、order_passengers
            $passenger_id_map = [];
            foreach ($order_passengers as $passenger) {
                $passenger['order_id'] = $insert_order_id;
                $passenger['origin_order_type'] = 1;
                $passenger['origin_order_id'] = $old_order['id'];
                $passenger['origin_passenger_id'] = $passenger['id'];
                $passenger['status'] = 3;
                $passenger['created_at'] = time();
                $passenger['updated_at'] = time();
                $old_passenger_id = $passenger['id'];
                unset($passenger['id']);
                $insert_passenger_id = $refund_pax_model->insert($passenger);
                $passenger_id_map[$old_passenger_id] = $insert_passenger_id;
            }
            foreach ($add_order_detail as $key => $val) {
                $val['order_id'] = $insert_order_id;
                $val['order_passenger_id'] = $passenger_id_map[$val['order_passenger_id']];;
                $refund_order_detail_model->insert($val);
            }
            // 3.3写入order_segments
            // TODO 需要指定退哪些航段
            $old_order_segments = $order_segment_model->where('order_id', $old_order_id)->findAll();
            foreach ($old_order_segments as $order_segment) {
                $order_segment['order_id'] = $insert_order_id;
                $order_segment['created_at'] = time();
                $order_segment['updated_at'] = time();
                unset($order_segment['id']);
                $refund_seg_model->insert($order_segment);
            }

            // 写入order_passenger_segments
            $old_order_passenger_segments = $order_passenger_segment_model->whereIn('passenger_id', $passenger_ids)->findAll();
            foreach ($old_order_passenger_segments as $order_passenger_segments) {
                $order_passenger_segments['passenger_id'] = $passenger_id_map[$order_passenger_segments['passenger_id']];
                $order_passenger_segments['ticket_status'] = 'REFUNDED';
                $order_passenger_segments['status'] = 5;
                unset($order_passenger_segments['id']);
                $refund_order_passenger_segment_model->insert($order_passenger_segments);
            }

            // 如果有分离PNR，则更新order_id到PNR表
            if ($is_split_pnr) {
                $pnr_model->where('id', $pnr_id)->set('order_id', $insert_order_id)->update();
            }

            // 更新原有乘客表的状态为已退票
            foreach ($passenger_id_map as $k => $v) {
                $order_passenger_model->where('id', $k)->where('order_id', $old_order_id)->set('status', 3)->update();
            }
            // 更新原有乘客航段表的状态为已退票
            foreach ($passenger_id_map as $k => $v) {
                $order_passenger_segment_model->where('passenger_id', $k)->where('status', 1)->set(['status' => 5, 'ticket_status' => 'REFUNDED'])->update();
            }

            $db->transComplete();
        } catch (\Exception $e) {
            $db->transRollback();
            error(0, $e->getMessage());
        }

        success('退票成功', ['order_id' => $insert_order_id]);
    }

    //国内退票订单详情
    public function orderDetail()
    {
        $validation = service('validation');
        $rules = [
            'order_id' => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getGet())) {
            $errors = $validation->getErrors();
            $error = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();
        $order_id = intval($validData['order_id']);

        $refund_order_model = model('TicketRefundOrderModel');
        $pnr_passenger_model = model('PnrPassengerModel');
        $user_model = model('UserModel');
        $department_model = model('DepartmentModel');
        $pnr_ticket_model = model('PnrTicketModel');
        $pnr_ticket_segment_model = model('PnrTicketSegmentModel');
        $airportModel = model('AirportModel');
        $airlineModel = model('AirlineModel');
        $cabinModel = model('CabinModel');
        $book_seg_model = model('TicketBookSegModel');
        $rebook_seg_model = model('TicketRebookSegModel');
        $refund_seg_model = model('TicketRefundSegModel');
        $flightModel = model('FlightModel');
        $order_detail_model = model('TicketRefundOrderDetailModel');
        $refund_pax_model = model('TicketRefundPaxModel');
        $refund_order_passenger_segment_model = model('TicketRefundPaxSegModel');

        //订单信息
        $order = $refund_order_model->find($order_id);
        if (empty($order)) {
            error(0, 'order_id错误');
        }
        $origin_order_id = $order['origin_order_id'];//原订单id
        $origin_order_no = $order['origin_order_no'];//原订单编号
        $origin_order_type = $order['origin_order_type'];//原订单编号
        $user = $user_model->find($order['operator_id']);
        $department = $department_model->find($user['department_id']);
        $order_data = [
            'order_no' => $order['order_no'],//订单编号
            'relate_order_no' => $origin_order_no,
            'pnr' => $order['pnr'],
            'customer_type' => OrderModel::CUSTOMER_TYPES[$order['customer_type']],//订单类型
            'customer_id' => $order['customer_id'],
            'status' => $order['status'],//订单状态
            'status_text' => OrderModel::ORDER_STATUS[$order['status']],//订单状态名称
            'customer_refund_flag' => $order['customer_refund_flag'],//退款状态
            'customer_refund_flag_text' => OrderModel::CUSTOMER_REFUND_FLAG[$order['customer_refund_flag']],//退款状态名称
            'created_at' => date('Y-m-d H:i:s', $order['created_at']),//下单时间
            'refunded_at' => date('Y-m-d H:i:s', $order['created_at']),//退款时间
            'operator_name' => $department['department_name'] . '|' . $user['name'],//操作员
            'journey_type' => $order['journey_type'],//航程类型
            'journey_type_text' => OrderModel::JOURNEY_TYPES[$order['journey_type']],//航程类型名称
            'refund_type' => $order['refund_type'],//退票类型
            'refund_type_text' => OrderModel::REFUND_TYPE[$order['refund_type']],//退票类型名称
            'contact_name' => $order['contact_name'],//联系人
            'contact_telephone' => $order['contact_telephone'],//联系电话
            'contact_email' => $order['contact_email'],//联系邮箱
            'is_send_sms' => $order['is_send_sms'],
            'remark' => $order['remark'],
        ];

        //乘客信息
        $passenger_data = [];
        $order_passengers = $refund_pax_model->where('order_id', $order_id)->findAll();
        $passenger_ids = array_column($order_passengers, 'id');

        $order_passenger_segment_arr = [];//按ticket_id分组
        $allow_flight_numbers = [];//允许退票的航班
        $order_passenger_segments = $refund_order_passenger_segment_model->whereIn('passenger_id', $passenger_ids)->findAll();
        foreach ($order_passenger_segments as $ps) {
            $ticket_id = $ps['passenger_id'];
            $order_passenger_segment_arr[$ticket_id][] = [
                'ticket_id' => $ticket_id,
                'flight_number' => $ps['flight_number'],
                'status' => $ps['status'],//只有在1时可用
                'status_text' => PnrTicketSegmentModel::STATUS[$ps['status']],
            ];

            if ($ps['status'] == 1) {
                $allow_flight_numbers[$ticket_id][] = $ps['flight_number'];
            }
        }
        foreach ($order_passengers as $pp) {
            $ticket_number = $pp['ticket_number'];
            $ticket_id = $pp['id'];
            /**
            【退改状态】 计算规则：
            1.只要有一张状态为 【1.可使用(OPEN FOR USE)】 则退改状态为：可退票
            2.一张 可使用 都没有，则为 不可退
            3.全部都是 【5.已退票(REFUNDED)】    则为 已退票
             **/
            //电子票号对应航段数组
            $passenger_segments = $order_passenger_segment_arr[$ticket_id];
            $available_qty = 0;//可退票数量
            $refunded_qyt = 0;//已退票数
            foreach ($passenger_segments as $ts) {
                if ($ts['status'] == 1) {
                    $available_qty += 1;
                } elseif ($ts['status'] == 5) {
                    $refunded_qyt += 1;
                }
            }
            if ($available_qty >= 1) {
                $refund_change_status = '可退票';
            } elseif ($refunded_qyt == count($passenger_segments)) {
                $refund_change_status = '已退票';
            } else {
                $refund_change_status = '不可退';
            }
            $passenger_data[] = [
                'rph' => $pp['rph'],//序号
                'passenger_id' => $ticket_id,
                'pnr_passenger_id' => $pp['id'],
                'order_id' => $order_id,
                'person_name' => $pp['person_name'],//乘客姓名
                'gender' => PnrPassengerModel::GENDER[$pp['gender']],//性别
                'passenger_type' => $pp['passenger_type'],//乘客类型
                'passenger_type_text' => PnrPassengerModel::PASSENGER_TYPE[$pp['passenger_type']],//乘客类型文案
                'doc_type' => $pp['doc_type'],//证件类型
                'doc_type_text' => PnrPassengerModel::DOC_TYPE[$pp['doc_type']],//证件类型文案
                'doc_id' => Tools\Idcard::mask($pp['doc_id']),//证件号码
                'telephone' => Tools\Char::mask_phone($pp['telephone']),//联系电话
                'ticket_number' => Tools\Char::mask_ticket($ticket_number),//票号
                'ticket_segments' => $order_passenger_segment_arr,//电子票号对应航段数组
                'allow_flight_numbers' => $allow_flight_numbers[$ticket_id] ?? [],
                'refund_change_status' => $refund_change_status//退改状态
            ];
        }
        //航班信息
        //城市
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');
        //航空公司
        $airlines = $airlineModel->select('id,airline_code,airline_cn')->findAll();
        $airlines = array_column($airlines, null, 'airline_code');

        $origin_segments = $refund_seg_model->where('order_id', $order_id)->findAll();
        
        $flights = $flightModel->whereIn('flight_number', array_column($origin_segments, 'flight_number'))->findAll();
        $flights = array_column($flights, null, 'flight_number');

        $flight_data = [];
        foreach ($origin_segments as $ps) {
            $flight = $flights[$ps['flight_number']];
            $airline_code = $flight['airline_code'];//航司编号2位字母
            //航司logo
            $airline_logo = '/static/images/airline/' . $flight['airline_code'] . '.png';
            //折扣、行李重量
            $cabin = $cabinModel->where(['airline' => $airline_code, 'cabin' => $ps['cabin']])->first();
            //航程类型标识
            //航程类型：1单程 2往返 3联程-两航段 4联程-多航段 5多航段
            switch ($order['journey_type']) {
                case 1://单程
                    $journey_name = '单程';
                    break;
                case 2:
                    if ($ps['rph'] == 1) {
                        $journey_name = '去程';
                    } else {
                        $journey_name = '返程';
                    }
                    break;
                case 3:
                case 4:
                case 5:
                    $journey_name = '第' . Tools\Char::to_chinase_num($ps['rph']) . '程';
                    break;
            }
            $flight_data[] = [
                'journey_name' => $journey_name,
                'airline_logo' => $airline_logo,
                'flight_number' => $ps['flight_number'],
                'airline_code' => $airline_code,//航司编号2位字母
                'airline_cn' => $airlines[$airline_code]['airline_cn'],//航空公司名称
                'air_equip_type' => $flight['air_equip_type'], //机型
                'meal' => '有餐食',//TODO:需要计算出来，此处暂写死
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],//城市+出发机场名称
                'arrival_airport_cn' => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],//城市+到达机场名称
                'departure_datetime' => $ps['departure_datetime'],//出发时间
                'arrival_datetime' => $ps['arrival_datetime'],//到达时间
                'cabin' => $ps['cabin'], //舱位编号
                'discount' => $cabin['discount'] . '折',//折扣
                'luggage_weight' => $cabin['luggage_weight'],//行李重量
                //'refund_rate' => '退票10%-45%', //退票范围 TODO:需要计算出来，此处暂写死
                'sub_cabin' => $ps['sub_cabin'],//产品编号
                'customer_explain' => '退票规定:起飞前7天《含)之前:Q舱的20%，起飞前7天(不含)之内至起飞前2天(含)之前:Q舱的30%，起飞前2天(不含)之内至起飞前4小时……'//客规说明
            ];
        }
        //价格信息
        $order_detail = $order_detail_model->where('order_id', $order_id)->findAll();
        $order_passengers = array_column($order_passengers, null, 'id');
        $price_data = [
            'purchase' => [//采购
                'detail' => [],
                'total' => [
                    'total_supplier_amount' => 0.00
                ]
            ],
            'sales' => [//销售
                'detail' => [],
                'total' => [
                    'total_customer_amount' => 0.00
                ]
            ]
        ];
        foreach ($order_detail as $key => $val) {
            $order_passenger = $order_passengers[$val['order_passenger_id']];
            //采购
            $supplier_amount = 0;
            $supplier_amount = bcadd($supplier_amount, $val['ticket_supplier_marketing_price'], 2);
            $supplier_amount = bcadd($supplier_amount, $val['ticket_supplier_tax_cn'], 2);
            $supplier_amount = bcadd($supplier_amount, $val['ticket_supplier_tax_yq'], 2);
            $supplier_amount = bcsub($supplier_amount, $val['ticket_supplier_agency_fee'], 2);
            $supplier_amount = bcadd($supplier_amount, $val['ticket_supplier_service_fee'], 2);
            $supplier_amount = bcadd($supplier_amount, $val['ticket_supplier_insurance_fee'], 2);
            $supplier_amount = bcadd($supplier_amount, $val['ticket_supplier_refund_service_fee'], 2);
            $supplier_amount = bcadd($supplier_amount, $val['ticket_supplier_deduction_fee'], 2);
            $price_data['purchase']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'passenger_id' => $order_passenger['id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'ticket_supplier_marketing_price' => $val['ticket_supplier_marketing_price'],
                'ticket_supplier_tax_cn' => $val['ticket_supplier_tax_cn'],
                'ticket_supplier_tax_yq' => $val['ticket_supplier_tax_yq'],
                'ticket_supplier_tax_xt' => $val['ticket_supplier_tax_xt'],
                'ticket_supplier_agency_fee' => $val['ticket_supplier_agency_fee'],
                'ticket_supplier_service_fee' => $val['ticket_supplier_service_fee'],
                'ticket_supplier_insurance_fee' => $val['ticket_supplier_insurance_fee'],
                'ticket_supplier_refund_service_fee' => $val['ticket_supplier_refund_service_fee'],
                'ticket_supplier_deduction_fee' => $val['ticket_supplier_deduction_fee'],
                'ticket_supplier_deduction_fee_rate' => $val['ticket_supplier_deduction_fee_rate'],
                'supplier_amount' => $supplier_amount,
            ];
            $price_data['purchase']['total']['total_supplier_amount'] = bcadd($price_data['purchase']['total']['total_supplier_amount'], $supplier_amount, 2);
            //销售
            $customer_amount = 0;
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_marketing_price'], 2);
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_tax_cn'], 2);
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_tax_yq'], 2);
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_adjust_fee'], 2);
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_service_fee'], 2);
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_insurance_fee'], 2);
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_refund_service_fee'], 2);
            $customer_amount = bcadd($customer_amount, $val['ticket_customer_deduction_fee'], 2);
            $price_data['sales']['detail'][] = [
                'order_id' => $order_passenger['order_id'],
                'passenger_id' => $order_passenger['id'],
                'ticket_number' => $order_passenger['ticket_number'],
                'person_name' => $order_passenger['person_name'],//乘客名字
                'ticket_customer_marketing_price' => $val['ticket_customer_marketing_price'],
                'ticket_customer_tax_cn' => $val['ticket_customer_tax_cn'],
                'ticket_customer_tax_yq' => $val['ticket_customer_tax_yq'],
                'ticket_customer_tax_xt' => $val['ticket_customer_tax_xt'],
                'ticket_customer_adjust_fee' => $val['ticket_customer_adjust_fee'],
                'ticket_customer_service_fee' => $val['ticket_customer_service_fee'],
                'ticket_customer_insurance_fee' => $val['ticket_customer_insurance_fee'],
                'ticket_customer_refund_service_fee' => $val['ticket_customer_refund_service_fee'],
                'ticket_customer_deduction_fee' => $val['ticket_customer_deduction_fee'],
                'ticket_customer_deduction_fee_rate' => $val['ticket_customer_deduction_fee_rate'],
                'customer_amount' => $customer_amount,
            ];
            $price_data['sales']['total']['total_customer_amount'] = bcadd($price_data['sales']['total']['total_customer_amount'], $customer_amount, 2);
        }
        $data = [
            'order_data' => $order_data,
            'flight_data' => $flight_data,
            'passenger_data' => $passenger_data,
            'price_data' => $price_data
        ];

        success('获取成功', $data);
    }

    //保存国内退款订单价格
    public function savePrice()
    {
        $validation = service('validation');
        $rules = [
            'order_id'                                             => ['label' => 'order_id', 'rules' => 'required|greater_than[0]'],
            'purchase'                                             => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'purchase.detail'                                      => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'purchase.detail.*.passenger_id'                       => ['label' => '乘客ID', 'rules' => 'required|greater_than[0]'],
            'purchase.detail.*.ticket_supplier_marketing_price'    => ['label' => '票面价', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_tax_cn'             => ['label' => '机场建设费', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_tax_yq'             => ['label' => '燃油附加费', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_agency_fee'         => ['label' => '采购代理费', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_service_fee'        => ['label' => '采购服务费', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_insurance_fee'      => ['label' => '采购保险费', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_refund_service_fee' => ['label' => '采购退票服务费', 'rules' => 'required|decimal'],
            'purchase.detail.*.ticket_supplier_deduction_fee'      => ['label' => '采购退票费', 'rules' => 'required|decimal'],
            'sales'                                                => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'sales.detail'                                         => ['label' => '价格信息', 'rules' => 'required|is_array'],
            'sales.detail.*.passenger_id'                          => ['label' => '乘客ID', 'rules' => 'required|greater_than[0]'],
            'sales.detail.*.ticket_customer_marketing_price'       => ['label' => '票面价', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_tax_cn'                => ['label' => '机场建设费', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_tax_yq'                => ['label' => '燃油附加费', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_adjust_fee'            => ['label' => '加价/让利', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_service_fee'           => ['label' => '销售服务费', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_insurance_fee'         => ['label' => '销售保险费', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_refund_service_fee'    => ['label' => '销售退票服务费', 'rules' => 'required|decimal'],
            'sales.detail.*.ticket_customer_deduction_fee'         => ['label' => '销售退票费', 'rules' => 'required|decimal'],
        ];
        $validation->setRules($rules);
        if (!$validation->run($this->request->getJSON(true))) {
            $errors = $validation->getErrors();
            $error  = array_shift($errors);
            error(0, $error);
        }
        $validData = $validation->getValidated();

        if (count($validData['purchase']['detail']) != count($validData['sales']['detail'])) {
            error(0, '数据错误');
        }
        $purchase_price_list = $validData['purchase']['detail'];
        $sales_price_list = $validData['sales']['detail'];
        $purchase_price_list = array_column($purchase_price_list, null, 'passenger_id');
        $sales_price_list = array_column($sales_price_list, null, 'passenger_id');
        $passengers = [];
        foreach ($purchase_price_list as $k => $v) {
            if (!isset($sales_price_list[$k])) {
                error(0, '数据错误');
            }
            $passengers[] = [
                'passenger_id'                       => $k,
                'ticket_supplier_marketing_price'    => $v['ticket_supplier_marketing_price'],
                'ticket_supplier_tax_cn'             => $v['ticket_supplier_tax_cn'],
                'ticket_supplier_tax_yq'             => $v['ticket_supplier_tax_yq'],
                'ticket_supplier_agency_fee'         => $v['ticket_supplier_agency_fee'],
                'ticket_supplier_service_fee'        => $v['ticket_supplier_service_fee'],
                'ticket_supplier_insurance_fee'      => $v['ticket_supplier_insurance_fee'],
                'ticket_supplier_refund_service_fee' => $v['ticket_supplier_refund_service_fee'],
                'ticket_supplier_deduction_fee'      => $v['ticket_supplier_deduction_fee'],
                'ticket_customer_marketing_price'    => $sales_price_list[$k]['ticket_customer_marketing_price'],
                'ticket_customer_tax_cn'             => $sales_price_list[$k]['ticket_customer_tax_cn'],
                'ticket_customer_tax_yq'             => $sales_price_list[$k]['ticket_customer_tax_yq'],
                'ticket_customer_adjust_fee'         => $sales_price_list[$k]['ticket_customer_adjust_fee'],
                'ticket_customer_service_fee'        => $sales_price_list[$k]['ticket_customer_service_fee'],
                'ticket_customer_insurance_fee'      => $sales_price_list[$k]['ticket_customer_insurance_fee'],
                'ticket_customer_refund_service_fee' => $sales_price_list[$k]['ticket_customer_refund_service_fee'],
                'ticket_customer_deduction_fee'      => $sales_price_list[$k]['ticket_customer_deduction_fee'],
            ];
        }
        $validData['passengers'] = $passengers;

        /**
         * @var \App\Services\RefundService $refundService
         */
        $refundService = load_service('RefundService');
        $data    = $refundService->savePrice($validData);

        success('成功', $data);
    }
}