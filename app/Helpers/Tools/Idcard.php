<?php

namespace App\Helpers\Tools;

class Idcard
{
    //校验身份证
    public static function validateIdcard($idcard)
    {
        $idcard    = strtoupper($idcard);
        $regx      = "/(^\d{15}$)|(^\d{17}([0-9]|X)$)/";
        $arr_split = array();
        if (!preg_match($regx, $idcard)) {
            return false;
        }

        //检查15位
        if (15 == strlen($idcard)) {
            $regx = "/^(\d{6})+(\d{2})+(\d{2})+(\d{2})+(\d{3})$/";
            @preg_match($regx, $idcard, $arr_split);
            //检查生日日期是否正确
            $dtm_birth = "19" . $arr_split[2] . '/' . $arr_split[3] . '/' . $arr_split[4];
            if (!strtotime($dtm_birth)) {
                return false;
            } else {
                return true;
            }
        }

        //检查18位
        $regx = "/^(\d{6})+(\d{4})+(\d{2})+(\d{2})+(\d{3})([0-9]|X)$/";
        @preg_match($regx, $idcard, $arr_split);
        $dtm_birth = $arr_split[2] . '/' . $arr_split[3] . '/' . $arr_split[4];
        if (!strtotime($dtm_birth)) { //检查生日日期是否正确
            return false;
        }

        //检验18位身份证的校验码是否正确。
        //校验位按照ISO 7064:1983.MOD 11-2的规定生成，X可以认为是数字10。
        $arr_int = array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
        $arr_ch  = array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
        $sign    = 0;
        for ($i = 0; $i < 17; $i++) {
            $b    = (int)$idcard[$i];
            $w    = $arr_int[$i];
            $sign += $b * $w;
        }
        $n       = $sign % 11;
        $val_num = $arr_ch[$n];
        if ($val_num != substr($idcard, 17, 1)) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 通过身份证号获取性别
     *
     * @param $idcard
     *
     * @return int
     */
    public static function get_sex($idcard): int
    {
        return substr($idcard, (strlen($idcard) == 18 ? -2 : -1), 1) % 2 ? 1 : 2;
    }

    /**
     * 通过身份证号获取年龄
     *
     * @param $idcard
     *
     * @return int
     */
    public static function get_age($idcard)
    {
        $year     = substr($idcard, 6, 4);
        $monthDay = substr($idcard, 10, 4);

        $age = date('Y') - $year;
        if ($monthDay > date('md')) {
            $age--;
        }

        return intval($age);
    }

    /**
     * 获取出生日期
     *
     * @param $idCard
     *
     * @return false|string
     */
    public static function getBirthday($idCard)
    {
        // 移除可能的空格并转换为大写（处理末位X的情况）
        $idCard = strtoupper(trim($idCard));
        $length = strlen($idCard);

        // 验证身份证长度合法性
        if ($length !== 15 && $length !== 18) {
            return '';
        }

        // 处理18位身份证
        if ($length === 18) {
            $birthPart = substr($idCard, 6, 8); // 提取第7-14位（YYYYMMDD）[2,5](@ref)
            $year      = substr($birthPart, 0, 4);
            $month     = substr($birthPart, 4, 2);
            $day       = substr($birthPart, 6, 2);
        } else {
            // 处理15位身份证（年份补全为19XX）
            $birthPart = substr($idCard, 6, 6); // 提取第7-12位（YYMMDD）[1,3](@ref)
            $year      = '19' . substr($birthPart, 0, 2); // 补全为四位年份
            $month     = substr($birthPart, 2, 2);
            $day       = substr($birthPart, 4, 2);
        }

        // 验证日期有效性（基础校验）
        if (!checkdate((int)$month, (int)$day, (int)$year) || // 检查日期是否合法
            ($month === '00' || $day === '00') // 排除月份或日期为00的情况[3](@ref)
        ) {
            return '';
        }

        return sprintf('%04d-%02d-%02d', $year, $month, $day); // 统一格式化为YYYY-MM-DD
    }

    /**
     * 证件号脱敏处理
     *
     * @param $idCard
     *
     * @return mixed|string
     */
    public static function mask($idCard)
    {
        $length = strlen($idCard);
        $prefix = 3; // 默认保留前3位
        $suffix = 2; // 默认保留后2位

        // 特殊类型证件处理
        if ($length >= 12 && preg_match('/^[A-Za-z0-9]{2}[0-9]{4}[A-Za-z0-9]{6,}$/', $idCard)) {
            // 特定格式的护照（如2字母+4数字+6位以上）：保留首尾各3位
            $prefix = $suffix = 3;
        } elseif ($length <= 4) {
            // 超短证件号保留首尾各1位
            $prefix = $suffix = max(1, floor($length / 2));
        }

        $maskLen = $length - $prefix - $suffix;

        // 动态脱敏规则
        if ($maskLen > 0) {
            $stars = str_repeat('*', $maskLen);

            return substr($idCard, 0, $prefix) . $stars . substr($idCard, -$suffix);
        }

        // 长度不足时直接返回或部分星号
        return $length > $prefix
            ? substr($idCard, 0, $prefix) . str_repeat('*', $length - $prefix)
            : $idCard;
    }
}
